[tool.poetry]
name = "centix-load"
version = "0.1.0"
description = "Integrations app for Holmatro."
authors = ["Harborn Digital <<EMAIL>>"]
readme = "README.md"
package-mode = false
packages = [
    { include = "src" },
]

[tool.poetry.dependencies]
python = "~3.12.0" # Also update tool.black and tool.mypy config when changed
centix-api-client = { path = "../http_api_clients/centix_api_client" }
centix-api-client-v2 = { path = "../http_api_clients/centix_api_client_v2" }
sentry-sdk = "^1.37.1"
pydantic = "^2.5.2"
pydantic-settings = "^2.3.3"
cachetools = "^5.5.0"
requests = "^2.32.3"
pyhumps = "^3.8.0"
six = "^1.17.0"
sqlalchemy = "^2.0.23"
alembic = "^1.12.1"
fastapi = "^0.115.6"
uvicorn = "^0.34.0"
cryptography = "^44.0.0"
pyodbc = "^5.2.0"
sqlalchemy-utils = "^0.41.2"
python-jose = "^3.3.0"
httpx = "^0.28.1"
authlib = "^1.4.1"
alembic-autogen-check = "^1.1.1"
aiocache = "^0.12.3"
email-validator = "^2.2.0"
tqdm = "^4.67.1"
woocommerce = "^3.0.0"
python-slugify = "^8.0.4"

[tool.poetry.group.dev.dependencies]
alembic-autogen-check = "^1.1.1"
black = "^24.10.0"
autoflake = "^2.3.1"
isort = "^5.13.2"
pytest = "^8.3.4"
pytest-cov = "^6.0.0"
pytest-timeout = "^2.3.1"
pytest-xdist = "^3.6.1"
pytest-random-order = "^1.1.1"
pytest-mock = "^3.14.0"
pytest-asyncio = "^0.25.3"
pytest-httpx = "^0.35.0"
vcrpy = "^7.0.0"
mypy = "^1.14.1"
flake8 = "^7.1.1"
flake8-bugbear = "^24.12.12"
flake8-deprecated = "^2.2.1"
flake8-executable = "^2.1.3"
flake8-simplify = "^0.21.0"
flake8-debugger = "^4.1.2"
flake8-cognitive-complexity = "^0.1.0"
flake8-annotations-complexity = "^0.0.8"
flake8-functions = "^0.0.8"
flake8-pyproject = "^1.2.3"
bandit = "^1.8.2"
pre-commit = "^4.0.1"
poethepoet = "^0.32.1"
tqdm = "^4.67.1"
types-requests = "<2.32.0.20241016"
types-cachetools = "^5.5.0.20240820"
types-tqdm = "^4.67.0.20241119"
types-python-jose = "^3.3.4.20240106"
pytest-recording = "^0.13.2"
ruff = "^0.12.8"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.pytest.ini_options]
minversion = "7.0"
testpaths = ["./*/tests"]

[tool.coverage.run]
omit = ["*/tests/*", "migrations/*"]

[tool.ruff]
target-version = "py312"
line-length = 120

[tool.ruff.lint]
select = [
    "E", # pycodestyle
    "F", # pyflakes
    "UP", # pyupgrade
    "S", # fake8-bandit
    "B", # flake8-bugbear
    "SIM", # flake8-simplify
    "EXE", # flake8-executable
    "T10", # flake8-debugger
    "I", # isort
    "PERF", # perflint
    "C", # complexity
    "PL", # pylint
    "RUF"
]
ignore = ["E203", "E266", "E501"]
extend-select = [
    "W291",  # trailing whitespace
    "W292",  # no newline at end of file
    "W293",  # blank line contains whitespace
]

[tool.ruff.lint.per-file-ignores]
"tests/*" = ["S", "PL"]
"migrations/*" = ["S"]
"dev/*" = ["S"]
"conftest.py" = ["S"]


[[tool.mypy.overrides]]
module = "sqlalchemy_utils.*"
ignore_missing_imports = true

[tool.poe.tasks]
fix = [
    { cmd = "ruff format ." },
    { cmd = "ruff check . --fix --unsafe-fixes" },
    { cmd = "mypy" },
]
test = "pytest --cov=/*/src --cov-report=term-missing:skip-covered --cov-report=lcov:coverage.info -n auto --dist loadfile --random-order --log-level ERROR --junitxml=test-results.xml"
retest = "pytest --lf"
security = "bandit -c pyproject.toml -r ."
qa = [
    { cmd = "flake8" },
    { cmd = "mypy --explicit-package-bases ." },
]
check-migrations = [
    { cmd = "alembic upgrade head" },
    { cmd = "alembic-autogen-check" },
]