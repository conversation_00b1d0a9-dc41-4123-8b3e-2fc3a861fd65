from unittest import mock

import pytest
from centix_api_client import Centix<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>rand, CentixAPIDTOReadObjectType, CentixAPIDTOReadSalesProduct

from src.data.mappers.centix_product_mapper import ProductMapper
from src.data.models.sync_force.syncforce_product_schema import Product as SyncForceProduct
from src.shared.enum import LanguageEnum
from tests.utils import get_mock_response


@pytest.fixture()
def sync_force_product() -> SyncForceProduct:
    data = get_mock_response("models/mock_data/get_file_contents_syncforce_product2.json")
    return SyncForceProduct(**data["Product"])


@pytest.fixture()
def centix_brand() -> CentixAPIDTOReadBrand:
    return CentixAPIDTOReadBrand(AutoID=1)


@pytest.fixture()
def centix_object_type() -> CentixAPIDTOReadObjectType:
    return CentixAPIDTOReadObjectType(AutoID=1)


@pytest.fixture()
def centix_product_mapper() -> mock.Mock:
    return mock.create_autospec(ProductMapper, instance=True)  # type: ignore


def test_centix_sales_product_properties(sync_force_product) -> None:
    # Act
    centix_sales_product_create = ProductMapper.to_centix_sales_product_properties(sync_force_product)

    # Assert
    assert centix_sales_product_create.max_working_pressure_bar == 720.0
    assert centix_sales_product_create.max_working_pressure_psi == 10443.0
    assert centix_sales_product_create.hip_classification == 67.0
    assert centix_sales_product_create.model == "PCU50"
    assert centix_sales_product_create.service_brand == "Holmatro"
    assert centix_sales_product_create.battery_included is False


def test_to_centix_sales_product(sync_force_product, centix_brand, centix_object_type) -> None:
    # Act
    centix_sales_product = ProductMapper.to_centix_read_sales_product(
        sync_force_product, centix_brand, centix_object_type
    )

    # Assert
    assert centix_sales_product.id == sync_force_product.article_number
    assert centix_sales_product.descr == sync_force_product.name.get_translation(LanguageEnum.EN)
    assert centix_sales_product.brand_id == centix_brand.auto_id
    assert centix_sales_product.object_type_id == centix_object_type.auto_id
    assert centix_sales_product.currency_id == 1
    assert centix_sales_product.purchase_currency_id == 1
    assert centix_sales_product.purchase_vat_id == 1
    assert centix_sales_product.stock_type == 1
    assert centix_sales_product.vat_id == 2
    assert centix_sales_product.status_id == 1


def test_to_centix_sales_product_translations(sync_force_product) -> None:
    # Act
    translations = ProductMapper.to_centix_sales_product_translations(sync_force_product)

    # Assert
    assert len(translations) > 0
    for translation in translations:
        assert translation.content_language in [LanguageEnum.NL.value, LanguageEnum.DE.value]
        assert translation.var_property in ["Descr", "Descr2"]
        assert translation.value is not None


def test_to_centix_brand_type(sync_force_product, centix_brand, centix_object_type) -> None:
    centix_product = CentixAPIDTOReadSalesProduct(AutoID=1)
    # Act
    brand_type = ProductMapper.to_centix_brand_type(
        centix_brand, centix_object_type, centix_product, sync_force_product
    )

    # Assert
    assert brand_type.brand_id == centix_brand.auto_id
    assert brand_type.default_object_type_id == centix_object_type.auto_id
    assert brand_type.default_product_id == centix_product.auto_id
    assert brand_type.archive is False
    assert brand_type.id == sync_force_product.article_number
    assert brand_type.descr == sync_force_product.name.get_translation(LanguageEnum.EN)


def test_to_brand_type_default_mi_plan() -> None:
    # Act
    mi_plan = ProductMapper.to_brand_type_default_mi_plan(brand_type_id=1, mi_plan_id=2)

    # Assert
    assert mi_plan.brand_type_id == 1
    assert mi_plan.mi_plan_id == 2
    assert mi_plan.time_schedule_id == 1
    assert mi_plan.mi_validation_expires is True
    assert mi_plan.mi_valid_until_date_calculation_method == 1
    assert mi_plan.next_run_calculation_method == 0
