from datetime import datetime
from unittest.mock import patch

import pytest
from centix_api_client import CentixAPIDTOCreateDocumentFromUrl

from src.data.mappers.centix_document_mapper import DocumentMapper
from src.domain.models.asset import AssetDetails
from src.domain.models.asset import File
from src.domain.models.asset import FileLanguage
from src.domain.models.asset import LanguageLabelTranslation
from src.domain.models.asset import Segmentation
from src.domain.models.document_kind import DocumentKind
from src.domain.models.product import Brand
from src.domain.models.product import Product
from src.domain.models.product import ProductAssets
from src.domain.models.product import ProductAttributes
from src.domain.models.product import ProductName
from src.shared.enum import CentixLanguageIds
from src.shared.enum import LanguageEnum


@pytest.fixture
def mock_env():
    with patch("src.data.mappers.centix_document_mapper.env") as mock:
        mock.datahub_api_base_url = "https://mock.api.base.url"
        yield mock


@pytest.fixture
def sample_file():
    return File(
        char="some_char",
        file_name="test_document.pdf",
        size=1024,
        cid="12345",
        type="Specification",
        expiry_url="https://mock.expiry.url",
        BREF="Some BREF",
        languages=[FileLanguage(language=LanguageEnum.EN)],
    )


@pytest.fixture
def sample_product():
    return Product(
        UMID="test_umid",
        article_number="test_article",
        brand=Brand(),
        name=ProductName(translations=[]),
        assets=ProductAssets(asset=[]),
        attributes=ProductAttributes(attribute=[]),
    )


@pytest.fixture
def asset_details(sample_file):
    return AssetDetails(
        UDAI="98765",
        reference_nr=123,
        change_date=datetime.now(),
        type_id=1,
        sub_type_id=1,
        translations=[LanguageLabelTranslation(language=LanguageEnum("en"), label="Test Label")],
        files=[sample_file],
        segmentation=Segmentation(),
    )


@pytest.fixture
def document_kind():
    return DocumentKind(id="DM", auto_id=8)


def test_to_centix_document(mock_env, sample_file, sample_product, asset_details, document_kind):
    # Arrange
    file_size = 1024
    file_extension = sample_file.file_name.split(".")[1]

    # First create a Document domain model using the new method
    document = DocumentMapper.create_document_from_assets(
        sample_file, asset_details, sample_product, file_size, LanguageEnum.EN, document_kind
    )

    # Then convert it to a Centix DTO
    centix_document = DocumentMapper.to_centix_create_document_from_url(document)

    # Test public document
    public_document_kind = DocumentKind(id="PM", auto_id=None)
    document_public = DocumentMapper.create_document_from_assets(
        sample_file, asset_details, sample_product, file_size, LanguageEnum.EN, public_document_kind
    )
    centix_document_public = DocumentMapper.to_centix_create_document_from_url(document_public)

    # Expected URL construction
    expected_url = (
        f"https://mock.api.base.url/api/products/assets?asset_id={asset_details.udai}"
        f"&file_name={sample_file.file_name}&desired_filename=Test%20Label.{file_extension}"
    )

    # Assert General Document Attributes
    assert isinstance(centix_document, CentixAPIDTOCreateDocumentFromUrl)
    assert centix_document.id == f"{sample_product.article_number}/{asset_details.reference_nr}/EN"
    assert centix_document.descr == "Test Label"
    assert centix_document.document_kind_id == document_kind.auto_id
    assert centix_document.language_id == CentixLanguageIds.EN.value
    assert centix_document.url == expected_url
    assert centix_document.file_name == "test_document"
    assert centix_document.file_extension == ".pdf"
    assert centix_document.file_size == file_size

    # Validate Expiry Date (if relevant)
    if centix_document.expire_date:
        assert isinstance(datetime.fromisoformat(centix_document.expire_date), datetime)

    # Assert Public Document URL
    expected_public_url = (
        f"https://mock.api.base.url/api/products/public/assets?asset_id={asset_details.udai}"
        f"&file_name={sample_file.file_name}&desired_filename=Test%20Label.{file_extension}"
    )
    assert centix_document_public.url == expected_public_url
