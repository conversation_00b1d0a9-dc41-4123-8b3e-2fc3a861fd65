from unittest.mock import Mock
from uuid import UUID

import pytest

from src.data.models.woocommerce.category import Category as WCCategory
from src.data.woocommerce_category_datasource import WoocommerceCategoryDatasource
from src.data.woocommerce_category_repository_impl import WoocommerceCategoryRepositoryImpl
from src.domain.models.proposition import Category
from src.domain.models.proposition import CategoryTranslation
from src.shared.enum import LanguageEnum


@pytest.fixture
def mock_woocommerce_api():
    """Mock WooCommerce API client."""
    return Mock()


@pytest.fixture
def mock_category_datasource():
    """Mock WooCommerce category datasource."""
    return Mock(spec=WoocommerceCategoryDatasource)


@pytest.fixture
def repository(mock_woocommerce_api, mock_category_datasource):
    """Create repository instance with mocked dependencies."""
    return WoocommerceCategoryRepositoryImpl(mock_woocommerce_api, mock_category_datasource)


@pytest.fixture
def sample_category():
    """Create a sample category with translations."""
    return Category(
        guid="12345678-1234-5678-1234-************",
        id=1,
        sort_index=0,
        products=[],
        translations=[
            CategoryTranslation(language=LanguageEnum.EN, label="Electronics"),
            CategoryTranslation(language=LanguageEnum.DE, label="Elektronik"),
            CategoryTranslation(language=LanguageEnum.FR, label="Électronique"),
        ],
        sub_categories=[],
    )


def test_create_english_category_first(repository, mock_category_datasource, sample_category):
    """Test that English categories are created first as main language."""
    # Setup
    mock_category_datasource.get_categories.return_value = []

    # Mock the create method to return a WCCategory
    created_english_category = WCCategory(
        id=100,
        guid=UUID(sample_category.guid),
        name="Electronics",
        slug="electronics",
        parent_id=0,
        menu_order=0,
        language="en",
        translations={},
    )
    mock_category_datasource.create.return_value = created_english_category

    # Mock the create_translation method
    created_german_category = WCCategory(
        id=101,
        guid=UUID(sample_category.guid),
        name="Elektronik",
        slug="elektronik",
        parent_id=0,
        menu_order=0,
        language="de",
        translations={"en": 100},
    )
    mock_category_datasource.create_translation.return_value = created_german_category

    # Execute
    repository.upsert([sample_category])

    # Verify English category was created first
    mock_category_datasource.create.assert_called_once()
    create_call_args = mock_category_datasource.create.call_args[0][0]
    assert create_call_args["name"] == "Electronics"
    assert create_call_args["guid"] == sample_category.guid

    # Verify translations were created with translation_of parameter
    assert mock_category_datasource.create_translation.call_count == 2  # DE and FR

    # Check German translation call
    german_call = mock_category_datasource.create_translation.call_args_list[0]
    german_payload, german_lang, translation_of_id, source_language = german_call.kwargs.values()
    assert german_payload["name"] == "Elektronik"
    assert german_lang == "de"
    assert translation_of_id == 100
    assert source_language == "en"
