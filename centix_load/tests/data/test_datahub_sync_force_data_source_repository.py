from unittest.mock import MagicMock, patch

import pytest
from pydantic import SecretStr

from src.data.datahub_sync_force_data_source_repository import DatahubProductRepositoryImpl
from src.data.mappers.sync_force_proposition_response_mapper import SyncForcePropositionResponseMapper
from src.data.models.sync_force.syncforce_product_schema import Product
from tests.utils import get_mock_response


@pytest.fixture()
def datasource() -> DatahubProductRepositoryImpl:
    return DatahubProductRepositoryImpl(base_url="http://test123.com", api_key=SecretStr("mock-api-key"))


@patch("requests.get")
def test_get_directory_files(mock_get, datasource) -> None:
    # Arrange
    response_data = get_mock_response("data/mock_responses/get_directory_files.json")

    mock_response = MagicMock()
    mock_response.json.return_value = response_data
    mock_get.return_value = mock_response

    # Act
    results = datasource.get_directory_files(sub_directory="product-details")

    # Assert
    assert len(results) == 5

    object_0 = results[0]
    assert object_0.key == "product-details-0.json"
    assert object_0.size == 11111

    object_1 = results[1]
    assert object_1.key == "product-details-1.json"
    assert object_1.size == 22222


@patch("requests.get")
def test_get_file_contents_syncforce_product(mock_get, datasource) -> None:
    # Arrange
    response_data = get_mock_response("data/mock_responses/get_file_contents_syncforce_product.json")

    mock_response = MagicMock()
    mock_response.json.return_value = response_data
    mock_get.return_value = mock_response

    file_name = "product-details-3da5c3bb-909a-4f1f-9621-24bb5550395b.json"

    # Act
    results = Product(**datasource.get_file_contents(file_name)["Product"])

    # Assert
    assert results.umid == "3da5c3bb-909a-4f1f-9621-24bb5550395b"
    assert results.article_number == "100.570.036"

    assert results.languages[0].code == "de"
    assert results.header.markets[0].name == "China"  # type: ignore
    assert results.brand.brand_name == "Holmatro"
    assert results.hierarchy_level and results.hierarchy_level.id == 6
    assert len(results.assets.roles) == 2


@patch("requests.get")
def test_get_last_modified_header(mock_get, datasource) -> None:
    mock_response = MagicMock()
    mock_response.headers = {"Last-Modified": "Wed, 21 Oct 2015 07:28:00 GMT"}
    mock_get.return_value = mock_response

    results = datasource.get_last_modified_header(file_path="product-details-3da5c3bb-909a-4f1f-9621-24bb5550395b.json")

    assert results == "Wed, 21 Oct 2015 07:28:00 GMT"


@patch("requests.get")
def test_get_file_contents_proposition(mock_get, datasource) -> None:
    # Arrange
    response_data = get_mock_response("data/mock_responses/get_file_contents_proposition.json")

    mock_response = MagicMock()
    mock_response.json.return_value = response_data
    mock_get.return_value = mock_response

    file_name = "proposition-17ef3b47-e729-42b8-9888-6a1350e835f0.json"

    # Act
    results = SyncForcePropositionResponseMapper.to_model(datasource.get_file_contents(file_name))

    # Assert
    assert results.categories[0].products is not None, "Expected products to be present"
    product_0 = results.categories[0].products[0]
    assert results.guid == "17ef3b47-e729-42b8-9888-6a1350e835f0"
    assert product_0.umid == "74370a33-3e6b-446f-ab24-5a415c0feb52"
    assert product_0.pmid == "31e5f8c6-6a0f-47aa-83a5-02fe7d543789"
    assert product_0.digital_life_cycle_status == "Released"
    assert product_0.sort_index == 0
    assert product_0.star is False


@patch("requests.get")
def test_get_proposition_by_id(mock_get, datasource) -> None:
    # Arrange
    response_data = get_mock_response("data/mock_responses/get_file_contents_proposition.json")

    mock_response = MagicMock()
    mock_response.json.return_value = response_data
    mock_get.return_value = mock_response

    proposition_id = "17ef3b47-e729-42b8-9888-6a1350e835f0"

    # Act
    results = datasource.get_proposition_by_id(proposition_id)

    # Assert
    product_0 = results.categories[0].products[0]
    assert results.guid == "17ef3b47-e729-42b8-9888-6a1350e835f0"
    assert product_0.umid == "74370a33-3e6b-446f-ab24-5a415c0feb52"
    assert product_0.pmid == "31e5f8c6-6a0f-47aa-83a5-02fe7d543789"
    assert product_0.digital_life_cycle_status == "Released"
    assert product_0.sort_index == 0
    assert product_0.star is False


@patch("requests.get")
def test_get_asset_by_id(mock_get, datasource) -> None:
    # Arrange
    response_data = {
        "UDAI": "17ef3b47-e729-42b8-9888-6a1350e835f1",
        "referenceNr": 1,
        "changeDate": "2023-01-01T00:00:00Z",
        "typeId": 1,
        "subTypeId": 39,
        "translations": [],
        "files": [
            {
                "char": "G",
                "fileName": "file1.pdf",
                "cid": "some_cid",
                "type": "some_type",
                "expiryUrl": "http://example.com/expiry",
                "BREF": "some_bref",
            }
        ],
        "segmentation": {"targetGroups": [], "brands": [], "markets": []},
    }

    directory_listing = [
        {
            "key": "assets/17ef3b47-e729-42b8-9888-6a1350e835f1/file1.pdf",
            "size": 12345,
            "last_modified": "2023-01-01T00:00:00Z",
        }
    ]

    # First call returns asset details, second call returns directory listing
    mock_response_asset = MagicMock()
    mock_response_asset.json.return_value = response_data

    mock_response_dir = MagicMock()
    mock_response_dir.json.return_value = directory_listing

    mock_get.side_effect = [mock_response_asset, mock_response_dir]

    asset_id = "17ef3b47-e729-42b8-9888-6a1350e835f1"

    # Act
    results = datasource.get_asset_by_id(asset_id)

    # Assert
    assert results.udai == "17ef3b47-e729-42b8-9888-6a1350e835f1"
    assert results.reference_nr == 1
    assert results.type_id == 1
    assert results.sub_type_id == 39
    assert len(results.files) == 1
    assert results.files[0].char == "G"
    assert results.files[0].file_name == "file1.pdf"
