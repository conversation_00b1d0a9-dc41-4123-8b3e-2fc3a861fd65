from unittest.mock import MagicMock
from unittest.mock import patch

import pytest

from src.data.centix_product_repository_impl import CentixProductRepositoryImpl
from src.exceptions import CentixLoadException
from src.exceptions import TooManyObjectsException


@pytest.fixture
def centix_product_data_source():
    """Fixture to create CentixProductDataSource with mocked dependencies."""
    mock_api_client = MagicMock()
    mock_api_client_v2 = MagicMock()
    settings = MagicMock()
    product_mapper = MagicMock()
    document_datasource = MagicMock()
    return CentixProductRepositoryImpl(
        settings, mock_api_client, mock_api_client_v2, product_mapper, document_datasource
    )


def test_get_product_by_article_number(centix_product_data_source):
    mock_response = MagicMock()
    mock_response.total_count = 1
    mock_response.items = ["Product"]

    centix_product_data_source._product_client.sales_products_o_data_list = MagicMock(return_value=mock_response)

    result = centix_product_data_source.get_product_by_article_number("12345")

    assert result == "Product"
    centix_product_data_source._product_client.sales_products_o_data_list.assert_called_once_with(
        filter="ID eq '12345'"
    )


def test_get_product_by_article_number_multiple(centix_product_data_source):
    mock_response = MagicMock()
    mock_response.total_count = 2
    mock_response.items = ["Product1", "Product2"]

    centix_product_data_source._product_client.sales_products_o_data_list = MagicMock(return_value=mock_response)

    with pytest.raises(TooManyObjectsException):
        centix_product_data_source.get_product_by_article_number("12345")

    centix_product_data_source._product_client.sales_products_o_data_list.assert_called_once_with(
        filter="ID eq '12345'"
    )


@patch("src.data.centix_product_repository_impl.ProductsApi")
def test_patch_product_properties(mock_products_api, centix_product_data_source):
    properties = MagicMock()
    properties.model_dump.return_value = {"field": "value"}

    centix_product_data_source._product_client = mock_products_api.return_value

    centix_product_data_source.patch_product_properties(1, properties)

    mock_products_api.return_value.sales_products_patch.assert_called_once_with(
        autoid=1, update_properties={"field": "value"}
    )


@patch("src.data.centix_product_repository_impl.BrandsApi")
def test_get_brand_by_id(mock_brands_api, centix_product_data_source):
    mock_brand = MagicMock(id="test_brand")
    mock_brands_api.return_value.brands_get_list.return_value = [mock_brand]

    result = centix_product_data_source.get_brand_by_id("test_brand")

    assert result == mock_brand
    mock_brands_api.return_value.brands_get_list.assert_called_once()


@patch("src.data.centix_product_repository_impl.BrandsApi")
def test_get_brand_by_id_not_found(mock_brands_api, centix_product_data_source):
    mock_brand = MagicMock(id="test_brand")
    mock_brands_api.return_value.brands_get_list.return_value = [mock_brand]

    with pytest.raises(CentixLoadException):
        centix_product_data_source.get_brand_by_id("non_existent")


def test_get_miplan_by_id(centix_product_data_source):
    mock_plan = MagicMock()
    mock_plan.ID = "test_plan"
    mock_plan.id = "test_plan"
    mock_plan.model_dump.return_value = {"id": "test_plan"}
    centix_product_data_source._miplan_client.m_i_plans_get_list = MagicMock(return_value=[mock_plan])

    result = centix_product_data_source.get_miplan_by_id("test_plan")

    assert result.id == "test_plan"
    centix_product_data_source._miplan_client.m_i_plans_get_list.assert_called_once()


@patch("src.data.centix_product_repository_impl.ProductGroupsApi")
def test_get_product_group_by_id(mock_product_groups_api, centix_product_data_source):
    mock_group = MagicMock(id="group_id")
    mock_product_groups_api.return_value.product_groups_get_list.return_value = [mock_group]

    result = centix_product_data_source.get_product_group_by_id("group_id")

    assert result == mock_group
    mock_product_groups_api.return_value.product_groups_get_list.assert_called_once()


def test_get_brand_type_by_id_single(centix_product_data_source):
    mock_item = MagicMock()
    mock_item.ID = "type_id"
    mock_item.Archive = False
    mock_item.model_dump.return_value = {"id": "type_id", "archive": False}
    mock_response = MagicMock()
    mock_response.items = [mock_item]
    centix_product_data_source._brand_types_client_v2.brand_types_o_data_list = MagicMock(return_value=mock_response)

    result = centix_product_data_source.get_brand_type_by_id("type_id")

    assert result.id == "type_id"
    centix_product_data_source._brand_types_client_v2.brand_types_o_data_list.assert_called_once()


def test_get_brand_type_by_id_multiple(centix_product_data_source):
    mock_item1 = MagicMock()
    mock_item1.ID = "type_id"
    mock_item1.Archive = False
    mock_item1.archive = False
    mock_item1.model_dump.return_value = {"id": "type_id", "archive": False}

    mock_item2 = MagicMock()
    mock_item2.ID = "type_id"
    mock_item2.Archive = True
    mock_item2.archive = True
    mock_item2.model_dump.return_value = {"id": "type_id", "archive": True}

    mock_response = MagicMock()
    mock_response.items = [mock_item1, mock_item2]
    centix_product_data_source._brand_types_client_v2.brand_types_o_data_list = MagicMock(return_value=mock_response)

    result = centix_product_data_source.get_brand_type_by_id("type_id")

    assert result.id == "type_id"
    centix_product_data_source._brand_types_client_v2.brand_types_o_data_list.assert_called_once()


def test_create_brand_type(centix_product_data_source):
    mock_brand_type = MagicMock()
    mock_create_method = MagicMock()
    centix_product_data_source._brand_types_client.brand_types_create = mock_create_method
    centix_product_data_source.create_brand_type(mock_brand_type)

    mock_create_method.assert_called_once_with(mock_brand_type)


def test_update_brand_type(centix_product_data_source):
    auto_id = 1
    mock_brand_type = MagicMock()
    mock_update_method = MagicMock()
    centix_product_data_source._brand_types_client.brand_types_update = mock_update_method
    centix_product_data_source.update_brand_type(auto_id, mock_brand_type)

    mock_update_method.assert_called_once_with(auto_id, mock_brand_type)


def test_delete_brand_type(centix_product_data_source):
    auto_id = 1
    mock_delete_method = MagicMock()
    centix_product_data_source._brand_types_client.brand_types_delete = mock_delete_method
    centix_product_data_source.delete_brand_type(auto_id)

    mock_delete_method.assert_called_once_with(auto_id)


def test_create_product(centix_product_data_source):
    mock_product = MagicMock()
    mock_create_method = MagicMock()
    centix_product_data_source._product_client.sales_products_create = mock_create_method
    centix_product_data_source.create_product(mock_product)

    mock_create_method.assert_called_once_with(mock_product)


def test_update_product(centix_product_data_source):
    auto_id = 1
    mock_product = MagicMock()
    mock_update_method = MagicMock()
    centix_product_data_source._product_client.sales_products_update = mock_update_method
    centix_product_data_source.update_product(auto_id, mock_product)

    mock_update_method.assert_called_once_with(auto_id, mock_product)


def test_delete_product(centix_product_data_source):
    auto_id = 1
    mock_delete_method = MagicMock()
    centix_product_data_source._product_client.sales_products_delete = mock_delete_method
    centix_product_data_source.delete_product(auto_id)

    mock_delete_method.assert_called_once_with(auto_id)


def test_patch_product_translations(centix_product_data_source):
    auto_id = 1
    mock_translations = MagicMock()
    mock_patch_method = MagicMock()
    centix_product_data_source._product_client.sales_products_patch_translated_properties = mock_patch_method
    centix_product_data_source.patch_product_translations(auto_id, mock_translations)

    mock_patch_method.assert_called_once_with(auto_id, mock_translations)


def test_update_image_for_product(centix_product_data_source):
    auto_id = 1
    img_path = "path/to/image.jpg"
    mock_update_image_method = MagicMock()
    centix_product_data_source._product_client.sales_products_set_images = mock_update_image_method
    centix_product_data_source.update_image_for_product(auto_id, img_path)

    mock_update_image_method.assert_called_once_with(auto_id, img_path)


def test_delete_image_for_product(centix_product_data_source):
    auto_id = 1
    mock_delete_image_method = MagicMock()
    centix_product_data_source._product_client.sales_products_remove_images = mock_delete_image_method
    centix_product_data_source.delete_image_for_product(auto_id)

    mock_delete_image_method.assert_called_once_with(auto_id)


def test_get_default_mi_plan_for_brand_type_single(centix_product_data_source):
    mock_schedule = MagicMock()
    centix_product_data_source._brand_types_client.brand_types_default_mi_schedules = MagicMock(
        return_value=[mock_schedule]
    )

    result = centix_product_data_source.get_default_mi_plan_for_brand_type(1)

    assert result == mock_schedule
    centix_product_data_source._brand_types_client.brand_types_default_mi_schedules.assert_called_once_with(1)


def test_get_default_mi_plan_for_brand_type_multiple(centix_product_data_source):
    centix_product_data_source._brand_types_client.brand_types_default_mi_schedules = MagicMock(
        return_value=[MagicMock(), MagicMock()]
    )

    with pytest.raises(TooManyObjectsException):
        centix_product_data_source.get_default_mi_plan_for_brand_type(1)

    centix_product_data_source._brand_types_client.brand_types_default_mi_schedules.assert_called_once_with(1)


def test_get_default_mi_plan_for_brand_type_none(centix_product_data_source):
    centix_product_data_source._brand_types_client.brand_types_default_mi_schedules = MagicMock(return_value=[])

    result = centix_product_data_source.get_default_mi_plan_for_brand_type(1)

    assert result is None
    centix_product_data_source._brand_types_client.brand_types_default_mi_schedules.assert_called_once_with(1)


def test_create_default_mi_plan(centix_product_data_source):
    mock_plan = MagicMock()
    mock_create_method = MagicMock()
    mock_created = MagicMock()
    mock_created.model_dump.return_value = {"auto_id": 1}
    mock_create_method.return_value = mock_created

    with patch("src.data.centix_product_repository_impl.BrandTypeDefaultMISchedulesApi") as mock_api:
        mock_api.return_value.brand_type_default_mi_schedules_create = mock_create_method
        result = centix_product_data_source.create_default_mi_plan(mock_plan)

        mock_create_method.assert_called_once_with(mock_plan)
        assert result.auto_id == 1


def test_delete_default_mi_plan(centix_product_data_source):
    mock_delete_method = MagicMock()

    with patch("src.data.centix_product_repository_impl.BrandTypeDefaultMISchedulesApi") as mock_api:
        mock_api.return_value.brand_type_default_mi_schedules_delete = mock_delete_method
        centix_product_data_source.delete_default_mi_plan(1)

        mock_delete_method.assert_called_once_with(1)
