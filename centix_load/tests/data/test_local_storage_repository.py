import json
import os
from unittest.mock import MagicMock
from unittest.mock import mock_open
from unittest.mock import patch
from uuid import uuid4

import pytest

from src.data.local_file_storage_repository import LocalFileStorageRepository
from src.domain.models.sync_config import SyncConfig
from src.domain.models.sync_config import SyncType
from src.domain.models.sync_metadata.sync_metadata_directory_file import SyncMetaDataDirectoryFile
from src.shared.env import Env


@pytest.fixture
def mock_env():
    return Env(mounted_files_path="/mock/path")


@pytest.fixture
def repository(mock_env):
    return LocalFileStorageRepository(env=mock_env)


@pytest.fixture
def sync_config():
    return SyncConfig(proposition_id=uuid4(), sync_type=SyncType.CENTIX)


def test_write_sync_metadata(repository, sync_config):
    # Arrange
    mock_sync_metadata = MagicMock(spec=SyncMetaDataDirectoryFile)
    mock_sync_metadata.model_dump.return_value = {"key": "value"}
    mock_file_path = os.path.join(repository.sync_meta_data_file_path, repository._sync_metadata_filename(sync_config))

    with patch("builtins.open", mock_open()) as mocked_file:
        # Act
        repository.write_sync_metadata(mock_sync_metadata, sync_config)

        # Assert
        mocked_file.assert_called_once_with(mock_file_path, "w")
        mocked_file().write.assert_called_once_with(json.dumps({"key": "value"}, default=str))


def test_write_sync_metadata_exception(repository, sync_config):
    # Arrange
    mock_sync_metadata = MagicMock(spec=SyncMetaDataDirectoryFile)
    mock_sync_metadata.model_dump.side_effect = Exception("Mocked exception")

    # Act & Assert
    with pytest.raises(Exception, match="Failed to write sync metadata to file. Reason: Mocked exception"):
        repository.write_sync_metadata(mock_sync_metadata, sync_config)


def test_read_sync_metadata_success(repository, sync_config):
    # Arrange
    mock_file_content = '{"key": "value", "last_synced": "2023-01-01T00:00:00+00:00"}'
    mock_file_path = os.path.join(repository.sync_meta_data_file_path, repository._sync_metadata_filename(sync_config))

    with (
        patch("builtins.open", mock_open(read_data=mock_file_content)) as mocked_file,
        patch("src.data.local_file_storage_repository.SyncMetaDataDirectoryFile") as MockSyncMetaDataDirectoryFile,
    ):
        MockSyncMetaDataDirectoryFile.return_value = MagicMock()

        # Act
        result = repository.read_sync_metadata(sync_config)

        # Assert
        mocked_file.assert_called_once_with(mock_file_path, "r")
        assert isinstance(result, MagicMock)
        MockSyncMetaDataDirectoryFile.assert_called_once_with(last_synced="2023-01-01T00:00:00+00:00", key="value")


def test_read_sync_metadata_failure(repository, sync_config):
    # Arrange
    mock_file_path = os.path.join(repository.sync_meta_data_file_path, repository._sync_metadata_filename(sync_config))

    with patch("builtins.open", mock_open()) as mocked_file:
        mocked_file.side_effect = Exception("Mocked exception")

        # Act & Assert
        with pytest.raises(Exception, match="Failed to read sync metadata from file. Reason: Mocked exception"):
            repository.read_sync_metadata(sync_config)
        mocked_file.assert_called_once_with(mock_file_path, "r")
