from uuid import UUID

from src.data.category_sync_helper import CategorySyncHelper
from src.data.models.woocommerce.category import Category as WCCategory
from src.domain.models.proposition import Category
from src.domain.models.proposition import CategoryTranslation
from src.shared.enum import LanguageEnum


class TestWoocommerceCategorySyncHelper:
    def test_build_used_slugs_by_parent_and_language(self):
        existing_categories = [
            WCCategory(
                id=1,
                guid=UUID("12345678-1234-5678-1234-************"),
                name="Electronics",
                slug="electronics",
                parent_id=0,
                menu_order=0,
                language="en",
                translations={},
            ),
            WCCategory(
                id=2,
                guid=UUID("12345678-1234-5678-1234-************"),
                name="Elektronik",
                slug="elektronik",
                parent_id=0,
                menu_order=0,
                language="de",
                translations={"en": 1},
            ),
            WCCategory(
                id=3,
                guid=UUID("*************-8765-4321-************"),
                name="Phones",
                slug="phones",
                parent_id=1,
                menu_order=0,
                language="en",
                translations={},
            ),
        ]

        used_slugs = CategorySyncHelper.build_used_slugs_by_parent_and_language(existing_categories)

        assert "electronics" in used_slugs["en"][0]  # Parent 0, English
        assert "elektronik" in used_slugs["de"][0]  # Parent 0, German
        assert "phones" in used_slugs["en"][1]  # Parent 1, English
        assert len(used_slugs["en"][0]) == 1
        assert len(used_slugs["de"][0]) == 1
        assert len(used_slugs["en"][1]) == 1

    def test_group_existing_by_language_and_guid(self):
        existing_categories = [
            WCCategory(
                id=1,
                guid=UUID("12345678-1234-5678-1234-************"),
                name="Electronics",
                slug="electronics",
                parent_id=0,
                menu_order=0,
                language="en",
                translations={},
            ),
            WCCategory(
                id=2,
                guid=UUID("12345678-1234-5678-1234-************"),
                name="Elektronik",
                slug="elektronik",
                parent_id=0,
                menu_order=0,
                language="de",
                translations={"en": 1},
            ),
            WCCategory(
                id=3,
                guid=UUID("*************-8765-4321-************"),
                name="Clothing",
                slug="clothing",
                parent_id=0,
                menu_order=0,
                language="en",
                translations={},
            ),
        ]

        grouped = CategorySyncHelper.group_existing_by_language_and_guid(existing_categories)

        assert "en" in grouped
        assert "de" in grouped
        assert len(grouped["en"]) == 2
        assert len(grouped["de"]) == 1
        assert grouped["en"][UUID("12345678-1234-5678-1234-************")].name == "Electronics"
        assert grouped["de"][UUID("12345678-1234-5678-1234-************")].name == "Elektronik"

    def test_generate_slug_for_language(self):
        category = Category(
            guid="12345678-1234-5678-1234-************",
            id=1,
            sort_index=0,
            products=[],
            translations=[
                CategoryTranslation(language=LanguageEnum.EN, label="Electronics"),
                CategoryTranslation(language=LanguageEnum.DE, label="Elektronik"),
                CategoryTranslation(language=LanguageEnum.FR, label="Électronique"),
            ],
            sub_categories=[],
        )

        english_slug = CategorySyncHelper.generate_slug_for_language(category, "en")
        german_slug = CategorySyncHelper.generate_slug_for_language(category, "de")
        french_slug = CategorySyncHelper.generate_slug_for_language(category, "fr")

        assert english_slug == "electronics"
        assert german_slug == "elektronik"
        assert french_slug == "electronique"

    def test_get_parent_id_for_language(self):
        parent_guid = "abcdef12-3456-7890-abcd-ef1234567890"
        existing_by_lang_guid = {
            "en": {
                UUID(parent_guid): WCCategory(
                    id=50,
                    guid=UUID(parent_guid),
                    name="Parent",
                    slug="parent",
                    parent_id=0,
                    menu_order=0,
                    language="en",
                    translations={},
                )
            },
            "de": {
                UUID(parent_guid): WCCategory(
                    id=51,
                    guid=UUID(parent_guid),
                    name="Eltern",
                    slug="eltern",
                    parent_id=0,
                    menu_order=0,
                    language="de",
                    translations={"en": 50},
                )
            },
        }

        english_parent_id = CategorySyncHelper.get_parent_id_for_language(parent_guid, existing_by_lang_guid, "en")
        german_parent_id = CategorySyncHelper.get_parent_id_for_language(parent_guid, existing_by_lang_guid, "de")
        nonexistent_parent_id = CategorySyncHelper.get_parent_id_for_language(
            "fedcba09-8765-4321-fedc-ba0987654321", existing_by_lang_guid, "en"
        )

        assert english_parent_id == 50
        assert german_parent_id == 51
        assert nonexistent_parent_id == 0
