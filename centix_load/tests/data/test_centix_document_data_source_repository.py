from unittest.mock import MagicMock, patch

import pytest
from centix_api_client import CentixAPIDTOCreateDocumentFromUrl, CentixAPIDTOReadDocument

from src.data.centix_document_datasource import CentixDocumentDatasource
from src.data.mappers.centix_document_mapper import DocumentMapper
from src.domain.models.document import Document
from src.shared.env import Env


@pytest.fixture
def mock_env():
    return MagicMock(spec=Env)


@pytest.fixture
def mock_api_client():
    return MagicMock()


@pytest.fixture
def data_source(mock_api_client):
    return CentixDocumentDatasource(centix_api_client=mock_api_client, settings=mock_env)


def test_delete_document_by_aid(data_source):
    with patch.object(data_source._document_client, "documents_delete") as mock_delete:
        data_source.delete_document_by_aid(document_aid=123)
        mock_delete.assert_called_once_with(autoid=123)


def test_get_document_by_unique_id(data_source):
    # Create a mock with specific attribute values that will pass Pydantic validation
    mock_document = MagicMock(spec=CentixAPIDTOReadDocument)

    # Configure the mock to return appropriate values for the attributes
    mock_document.id = "mock_id"
    mock_document.descr = "mock_description"
    mock_document.document_kind_id = 1
    mock_document.language_id = 1
    mock_document.comment = "mock_comment"
    mock_document.expire_date = None
    mock_document.archive = False
    mock_document.archived_at = None
    mock_document.file_uri_type = None
    mock_document.file_name = "mock_file.txt"
    mock_document.file_extension = "txt"
    mock_document.file_checksum = "abcdef123456"
    mock_document.file_size = 1024
    mock_document.auto_rendered_report = False
    mock_document.document_handler_url = "http://example.com"
    mock_document.auto_id = 123
    mock_document.created_by = "user123"
    mock_document.time_stamp = None

    # Create a mock response
    mock_response = MagicMock()
    mock_response.items = [mock_document]

    # Patch the API call to return our mock response
    with patch.object(data_source._document_client, "documents_o_data_list", return_value=mock_response) as mock_list:
        # When we call get_document_by_unique_id
        result = data_source.get_document_by_unique_id(id="test_id")

        # The mapped Document should be returned
        # Create a properly mapped Document instance for comparison
        expected_document = DocumentMapper.to_domain(mock_document)
        assert result == expected_document

        # Verify the API was called with the correct filter
        mock_list.assert_called_once_with(filter="ID eq 'test_id'")


def test_get_document_by_unique_id_not_found(data_source):
    with patch.object(
        data_source._document_client, "documents_o_data_list", return_value=MagicMock(items=[])
    ) as mock_list:
        result = data_source.get_document_by_unique_id(id="test_id")
        assert result is None
        mock_list.assert_called_once_with(filter="ID eq 'test_id'")


def test_create_document(data_source):
    # Create a mock Document with the required attributes
    mock_domain_document = MagicMock(spec=Document)
    # Add the sanitized_file_name attribute that's being accessed in the mapper
    mock_domain_document.sanitized_file_name = "test_file.pdf"

    # Create mock for the return value from the API
    mock_centix_document = MagicMock(spec=CentixAPIDTOReadDocument)

    # Create a mock for the converted DTO
    mock_centix_dto = MagicMock(spec=CentixAPIDTOCreateDocumentFromUrl)

    # Mock the mapper's to_centix_create_document_from_url method
    with (
        patch.object(DocumentMapper, "to_centix_create_document_from_url", return_value=mock_centix_dto) as mock_to_dto,
        patch.object(DocumentMapper, "to_domain", return_value=mock_domain_document) as mock_to_domain,
        patch.object(
            data_source._document_client, "documents_create", return_value=mock_centix_document
        ) as mock_create,
    ):
        # Call the method under test
        result = data_source.create_document(document=mock_domain_document)

        # Verify the result is what we expect
        assert result == mock_domain_document

        # Verify the methods were called with the expected arguments
        mock_to_dto.assert_called_once_with(mock_domain_document)
        mock_create.assert_called_once_with(mock_centix_dto)
        mock_to_domain.assert_called_once_with(mock_centix_document)


def test_link_document_to_product(data_source):
    with patch.object(data_source._product_client, "sales_products_link_document") as mock_link:
        data_source.link_document_to_product(product_id=123, document_id=456)
        mock_link.assert_called_once_with(123, 456)


def test_get_product_documents(data_source):
    mock_documents = [MagicMock(spec=CentixAPIDTOReadDocument), MagicMock(spec=CentixAPIDTOReadDocument)]
    mock_domain_documents = [MagicMock(), MagicMock()]

    with (
        patch(
            "src.data.mappers.centix_document_mapper.DocumentMapper.to_domain", side_effect=mock_domain_documents
        ) as mock_mapper,
        patch.object(
            data_source._product_client,
            "sales_products_o_data_list_documents",
            return_value=MagicMock(items=mock_documents),
        ) as mock_list,
    ):
        result = data_source.get_product_documents(product_id=123)

        assert result == mock_domain_documents
        mock_list.assert_called_once_with(123)
        assert mock_mapper.call_count == 2
        mock_mapper.assert_any_call(mock_documents[0])
        mock_mapper.assert_any_call(mock_documents[1])


def test_get_product_documents_empty(data_source):
    with patch.object(
        data_source._product_client,
        "sales_products_o_data_list_documents",
        return_value=MagicMock(items=[]),
    ) as mock_list:
        result = data_source.get_product_documents(product_id=123)
        assert result == []
        mock_list.assert_called_once_with(123)
