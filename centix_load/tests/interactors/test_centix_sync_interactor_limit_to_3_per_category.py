import os
import warnings
from uuid import UUID

import pytest
from pydantic import SecretStr
from vcr import VCR
from vcr.request import Request

from src.domain.models.sync_config import SyncConfig, SyncType
from src.sync_interactor_factory import SyncInteractorFactory
from tests.utils import get_mock_response

warnings.filterwarnings("ignore", category=DeprecationWarning, module="urrlib3")

SERVICE_PORTAL_PROPOSITION_ID = "17ef3b47-e729-42b8-9888-6a1350e835f0"
RESCUE_PRODUCT_GROUP_ID = "f5f8c74d-bd48-4c77-9f1d-4949f6e565da"
INDUSTRY_PRODUCT_GROUP_ID = "9c308035-7925-431a-9a1e-df65de1b0327"


@pytest.fixture(autouse=True)
def mock_env():
    class MockEnv:
        datahub_api_base_url = "https://holmatro-products-sync.harborn.dev"
        datahub_api_key = SecretStr("datahub_api_key")
        mounted_files_path = "tests/fixtures/mounted_files"
        mounted_file_name = "test_file.json"
        centix_api_base_url = "https://holmatro-dev.centix.com/api"
        centix_api_client_id = "client_id"
        centix_api_client_secret = "client_secret"
        centix_api_token_url = "https://holmatro-dev.centix.com/identity/connect/token"
        centix_force_update_documents = True
        centix_object_type_id = "D"

    return MockEnv()


@pytest.fixture
def interactor_with_mocked_dependencies(mock_env):
    return SyncInteractorFactory.build_interactor(
        settings=mock_env,
        sync_config=SyncConfig(sync_type=SyncType.CENTIX, proposition_id=UUID(SERVICE_PORTAL_PROPOSITION_ID)),
    )


@pytest.fixture
def mock_proposition_file_contents():
    """Fixture that returns mock proposition data."""
    return get_mock_response("fixtures/proposition-17ef3b47-e729-42b8-9888-6a1350e835f0.json")


def _filter_request_binary(request: Request) -> Request:
    """Filters binary data from requests before recording."""
    headers = request.headers
    content_type = headers.get("Content-Type", "")

    # Redact multipart form data
    if "multipart/form-data" in content_type:
        request.body = b"<<MULTIPART BODY REDACTED>>"

    # Redact binary body for non-GET/HEAD/OPTIONS requests
    method = request.method.upper()
    if method not in ("GET", "HEAD", "OPTIONS"):
        body = request.body
        if isinstance(body, bytes):
            try:
                body.decode("utf-8")
            except UnicodeDecodeError:
                request.body = b"<<BINARY REQUEST BODY REDACTED>>"

    return request


def _filter_response_binary(response: dict) -> dict:
    """Filters binary data from responses before recording."""
    headers = response.get("headers", {})
    content_type_header = headers.get("Content-Type", [""])
    content_type = content_type_header[0] if content_type_header else ""

    # Flag to track if redaction happened
    redacted = False

    if any(binary_type in content_type for binary_type in ["pdf", "image", "octet-stream"]):
        # Replace with BYTES placeholder
        response["body"] = {"string": b"<<BINARY CONTENT REDACTED>>"}
        redacted = True
    else:
        body_content = response.get("body")
        raw_body_bytes = None

        if isinstance(body_content, bytes):
            raw_body_bytes = body_content
        elif isinstance(body_content, dict) and isinstance(body_content.get("string"), bytes):
            raw_body_bytes = body_content.get("string")
        elif isinstance(body_content, dict) and isinstance(body_content.get("base64_string"), bytes):
            pass  # Handle base64 if needed

        if raw_body_bytes is not None:
            try:
                raw_body_bytes.decode("utf-8")  # Try decoding
            except UnicodeDecodeError:
                # If it fails, it's binary, redact it with BYTES placeholder
                response["body"] = {"string": b"<<BINARY CONTENT REDACTED>>"}
                redacted = True

    if redacted and headers:
        # Create a new headers dictionary excluding 'content-length'
        response["headers"] = {k: v for k, v in headers.items() if k.lower() != "content-length"}

    return response


vcr = VCR(
    cassette_library_dir="tests/fixtures/vcr_cassettes",
    record_mode="once",
    filter_headers=["Authorization"],
    before_record_request=_filter_request_binary,
    before_record_response=_filter_response_binary,
)


@pytest.mark.sequential
def test_execute(mocker, interactor_with_mocked_dependencies, mock_proposition_file_contents):
    """Test the execute method of CentixSyncInteractor."""
    os.makedirs("tests/fixtures/mounted_files", exist_ok=True)
    with open("tests/fixtures/mounted_files/sync_metadata-centix.json", "w") as f:
        f.write("")

    interactor = interactor_with_mocked_dependencies

    # Mock only the target filename
    mock_response = mock_proposition_file_contents
    # Ensure the repository object exists before trying to access its method
    original_method = interactor._source_product_repository.get_file_contents

    def conditional_mock(filename):
        if isinstance(filename, str) and "proposition-17ef3b47-e729-42b8-9888-6a1350e835f0.json" in filename:
            return mock_response
        return original_method(filename)

    mocker.patch.object(
        interactor._source_product_repository,
        "get_file_contents",
        side_effect=conditional_mock,
    )
    with vcr.use_cassette("test_execute.yaml"):
        interactor.execute()
