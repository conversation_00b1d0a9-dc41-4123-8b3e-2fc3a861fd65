import json
import os
from os import path
from typing import Any
from urllib.parse import urlparse

from requests import Response
from requests import Session
from requests.adapters import BaseAdapter


def get_mock_response(file_path: str) -> Any:
    with open(f"{path.dirname(__file__)}/{file_path}", "r") as f:
        response_data = json.load(f)
        return response_data


class FileMockAdapter(BaseAdapter):
    def __init__(self, fixture_root):
        self.fixture_root = fixture_root

    def send(self, request, **kwargs):
        parsed = urlparse(request.url)
        rel_path = parsed.path.lstrip("/")
        if parsed.query:
            rel_path += f"?{parsed.query}"
        file_path = os.path.join(self.fixture_root, rel_path)

        response = Response()
        response.status_code = 200
        response.url = request.url
        response.request = request
        response.headers["Content-Type"] = "application/json"

        try:
            with open(file_path, "rb") as f:
                response._content = f.read()
        except FileNotFoundError:
            response.status_code = 404
            response._content = b'{"error": "Fixture not found"}'

        return response

    def close(self):
        pass


def create_file_mock_session(fixture_root):
    session = Session()
    mock_adapter = FileMockAdapter(fixture_root)
    session.mount("http://", mock_adapter)
    session.mount("https://", mock_adapter)
    return session
