from unittest.mock import Mock, patch

import pytest
import requests

from src.shared.utils.auth import AuthorizationToken


@pytest.fixture
def auth() -> AuthorizationToken:
    return AuthorizationToken()


@patch("src.shared.utils.auth.requests.post")
def test_refresh_token_success(mock_post: Mock, auth: AuthorizationToken) -> None:
    mock_response = Mock()
    mock_response.raise_for_status = Mock()
    mock_response.json.return_value = {"access_token": "test_token", "expires_in": 300}
    mock_post.return_value = mock_response

    token = auth.refresh_token()

    assert token == "test_token"
    assert auth.cache["access_token"] == "test_token"


@patch("src.shared.utils.auth.requests.post")
def test_refresh_token_cache(mock_post: Mock, auth: AuthorizationToken) -> None:
    auth.cache["access_token"] = "cached_token"
    token = auth.refresh_token()

    assert token == "cached_token"
    mock_post.assert_not_called()


@patch("src.shared.utils.auth.requests.post")
def test_refresh_token_http_error(mock_post: Mock, auth: AuthorizationToken) -> None:
    mock_response = Mock()
    mock_response.raise_for_status.side_effect = requests.exceptions.HTTPError("HTTP Error")
    mock_post.return_value = mock_response

    with pytest.raises(Exception, match="Failed to refresh token due to HTTP error"):
        auth.refresh_token()


@patch("src.shared.utils.auth.requests.post")
def test_refresh_token_request_exception(mock_post: Mock, auth: AuthorizationToken) -> None:
    mock_post.side_effect = requests.exceptions.RequestException("Request Error")

    with pytest.raises(Exception, match="Failed to refresh token due to request error"):
        auth.refresh_token()


@patch("src.shared.utils.auth.requests.post")
def test_refresh_token_invalid_json(mock_post: Mock, auth: AuthorizationToken) -> None:
    mock_response = Mock()
    mock_response.raise_for_status = Mock()
    mock_response.json.side_effect = ValueError("Invalid token response format")
    mock_post.return_value = mock_response

    with pytest.raises(ValueError, match="Invalid token response format"):
        auth.refresh_token()


@patch("src.shared.utils.auth.requests.post")
@patch("src.shared.utils.auth.logger")
def test_refresh_token_invalid_response_data(mock_logger: Mock, mock_post: Mock, auth: AuthorizationToken) -> None:
    # Arrange
    mock_response = Mock()
    mock_response.raise_for_status = Mock()
    mock_response.json.return_value = {"invalid": "response"}  # Missing required fields
    mock_post.return_value = mock_response

    # Act & Assert
    with pytest.raises(ValueError, match="Invalid token response format"):
        auth.refresh_token()

    mock_logger.error.assert_called_once_with("Failed to parse token response: {'invalid': 'response'}")
    assert "access_token" not in auth.cache
