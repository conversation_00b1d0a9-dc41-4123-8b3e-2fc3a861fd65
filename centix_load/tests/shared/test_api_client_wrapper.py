from unittest.mock import Magic<PERSON>ock

import pytest

from src.shared.utils.api_client_wrapper import ApiClientWrapper


@pytest.fixture
def mock_api_client():
    return MagicMock()


@pytest.fixture
def mock_token_refresh_hook():
    return MagicMock()


@pytest.fixture
def api_client_wrapper(mock_api_client, mock_token_refresh_hook):
    return ApiClientWrapper(mock_api_client, mock_token_refresh_hook)


def test_refresh_token_called(api_client_wrapper, mock_api_client, mock_token_refresh_hook):
    mock_token_refresh_hook.return_value = "new_token"
    mock_api_client.some_api_call = MagicMock()

    api_client_wrapper.some_api_call()

    mock_token_refresh_hook.assert_called_once()
    assert mock_api_client.configuration.access_token == "new_token"
    mock_api_client.some_api_call.assert_called_once()


def test_refresh_token_not_callable(api_client_wrapper, mock_api_client):
    api_client_wrapper.token_refresh_hook = None
    mock_api_client.some_api_call = MagicMock()

    api_client_wrapper.some_api_call()

    mock_api_client.some_api_call.assert_called_once()


def test_getattr_non_callable(api_client_wrapper, mock_api_client):
    mock_api_client.some_attribute = "some_value"

    result = api_client_wrapper.some_attribute
    assert result == "some_value"


def test_getattr_callable(api_client_wrapper, mock_api_client):
    mock_api_client.some_api_call = MagicMock()

    api_client_wrapper.some_api_call()

    mock_api_client.some_api_call.assert_called_once()
