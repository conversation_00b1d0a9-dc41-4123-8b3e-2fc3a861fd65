ARG PYTHON_VERSION=3.12
FROM python:${PYTHON_VERSION}-slim

WORKDIR /app
RUN apt-get update && apt-get install -y --no-install-recommends build-essential && rm -rf /var/lib/apt/lists/*
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/app

RUN pip install --no-cache-dir poetry==1.8.2 && poetry config virtualenvs.create false

COPY centix_load/pyproject.toml centix_load/poetry.lock ./
COPY http_api_clients/ ./http_api_clients/

RUN sed -i 's|\.\./http_api_clients|http_api_clients|g' pyproject.toml

RUN poetry lock --no-update

ARG INSTALL_DEV=false
RUN bash -c "if [ '$INSTALL_DEV' = 'true' ] ; then poetry install --no-root ; else poetry install --no-root --only main ; fi"

COPY centix_load/src/ ./src/
COPY centix_load/Makefile ./Makefile

CMD ["make", "run"]