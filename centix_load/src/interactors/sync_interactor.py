import logging
import os
import re
import tempfile
from datetime import datetime
from datetime import timezone
from typing import List
from typing import Tuple
from uuid import UUID

import sentry_sdk

from src.data.local_file_storage_repository import LocalFileStorageRepository
from src.domain.models.asset import AssetDetails
from src.domain.models.directory_file import DirectoryFile
from src.domain.models.product import Product
from src.domain.models.proposition import Category
from src.domain.models.sync_config import SyncConfig
from src.domain.models.sync_metadata.sync_metadata_directory_file import SyncMetaDataDirectoryFile
from src.domain.repositories.datahub_product_repository import SourceProductRepository
from src.domain.repositories.target_category_repository import TargetCategoryRepository
from src.domain.repositories.target_product_repository import TargetProductRepository
from src.shared.enum import AssetRoleIds
from src.shared.env import Env

env = Env()


class SyncInteractor:
    def __init__(
        self,
        sync_config: SyncConfig,
        source_product_repository: SourceProductRepository,
        target_product_repository: TargetProductRepository,
        target_category_repository: TargetCategoryRepository | None,
        local_storage_repository: LocalFileStorageRepository,
    ):
        self._sync_config = sync_config
        self._source_product_repository = source_product_repository
        self._target_product_repository = target_product_repository
        self._target_category_repository = target_category_repository
        self._local_storage_repository = local_storage_repository

    def execute(self) -> None:
        logging.info(f"Fetching metadata-sync file for {self._sync_config.sync_type.value}.")
        try:
            sync_metadata = self._local_storage_repository.read_sync_metadata(self._sync_config)
            last_synced_datetime, dir_key = sync_metadata.last_synced, sync_metadata.key
            logging.info(f"Last synced datetime: {last_synced_datetime}")
        except Exception:
            last_synced_datetime, dir_key = datetime.min.replace(tzinfo=timezone.utc), ""
            logging.info(f"Metadata not found for {self._sync_config.sync_type.value}. Running full sync.")

        # Retrieve all products from DataHub.
        datahub_product_directories = self._source_product_repository.get_directory_files(
            sub_directory="product-details-"
        )

        # Sort retrieved products by last_modified and the key
        datahub_product_directories.sort(
            key=lambda product_directory: (product_directory.last_modified, product_directory.key)
        )

        # Filter out previously synced products
        product_directories_to_sync = self._filter_synced_products(
            datahub_product_directories, last_synced_datetime, dir_key
        )

        # Get proposition
        logging.info("Fetching proposition.")
        proposition = self._source_product_repository.get_proposition_by_id(str(self._sync_config.proposition_id))

        # Upsert categories
        if self._target_category_repository:
            self._target_category_repository.upsert(proposition.categories)

        # Collect all product UMIDs from the proposition (including nested categories)
        all_proposition_umids = self._collect_all_product_umids(proposition.categories)

        # Filter product directories to only those present in the proposition
        filtered_products = []
        for product_dir in product_directories_to_sync:
            product_id = self._extract_uuid(product_dir)
            if product_id and product_id in all_proposition_umids:
                filtered_products.append((product_id, product_dir.last_modified, product_dir.key))

        logging.info(f"Found {len(filtered_products)} products to sync.")

        # Fetch product details and convert to domain model products, pairing with directory's last_modified
        logging.info(f"Fetch product details for all filtered products: {len(filtered_products)}.")

        products: List[Tuple[Product, datetime, str, str]] = []
        for product_id, dir_last_modified, dir_key in filtered_products:
            product = self._source_product_repository.get_product_by_id(product_id)
            # Assign all proposition category paths for this product as leaf GUIDs
            category_paths = self._collect_category_paths_for_product(proposition.categories, product_id)
            product.leaf_category_guids = [UUID(path[-1].guid) for path in category_paths if path]
            products.append((product, dir_last_modified, dir_key, product_id))

        logging.info("Sync/upsert products to service portal")
        for index, (product, dir_last_modified, dir_key, product_id) in enumerate(products):
            sentry_sdk.set_context(
                "product",
                {"article_number": product.article_number, "product_id": f"product-details-{product_id}.json"},
            )
            logging.info(
                f"Syncing product {index + 1}/{len(products)}: {product.article_number}, with product-details-{product_id}.json"
            )

            # Get the main image asset for the product
            product_asset = product.assets.get_asset_id_for_role(AssetRoleIds.MAIN_IMAGE.value)
            img_path = None

            if product_asset is None or product_asset.udai == "00000000-0000-0000-0000-000000000000":
                logging.warning(f"No main image found for product {product.article_number}")
            else:
                # Get the asset details from the datahub
                asset = self._source_product_repository.get_asset_by_id(product_asset.udai)
                file = asset.get_first_file_for_char("E")
                if file:
                    logging.info(f"Downloading product image {file.file_name}")
                    img_path = self._download_asset(f"assets/{product_asset.udai}/{file.file_name}")
                else:
                    logging.warning(f"No image file type 'E' found for product {product.article_number}")

            # Get assets
            sorted_assets = sorted(list(set(product.assets.asset)), key=lambda asset: asset.udai)
            asset_details_list: List[AssetDetails] = []
            for product_asset in sorted_assets:
                logging.info(f"Collecting asset {product_asset.udai}")
                try:
                    asset_details_list.append(self._source_product_repository.get_asset_by_id(product_asset.udai))
                except Exception as e:
                    logging.warning(f"Failed to fetch asset details for {product_asset.udai}: {e}")
                    continue

            # upsert
            self._target_product_repository.upsert(
                product=product,
                assets=asset_details_list,
                img_path=img_path,
            )

            # Clean up the temporary file if it exists
            if img_path:
                os.remove(img_path)

            # Update sync metadata with the directory's last_modified timestamp
            try:
                self._local_storage_repository.write_sync_metadata(
                    SyncMetaDataDirectoryFile(last_synced=dir_last_modified, key=dir_key), self._sync_config
                )
            except Exception as e:
                logging.error(f"Failed to write sync metadata for {dir_key}: {e}")

        logging.info("Finished syncing products to service portal")

    def _extract_uuid(self, directory_file: DirectoryFile) -> str | None:
        """Extracts a UUID from the specified key in a dictionary."""
        uuid_pattern = r"[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}"

        if directory_file.key:
            match = re.search(uuid_pattern, directory_file.key)
            if match:
                return match.group()
        return None

    def _download_asset(self, asset_path: str) -> str:
        """Downloads an asset from the datahub to a local temporary file."""
        _, ext = os.path.splitext(asset_path)
        fd, file_path = tempfile.mkstemp(suffix=ext)

        with open(fd, "wb") as temp_file:
            temp_file.write(self._source_product_repository.get_file_contents(asset_path))

        return file_path

    def _filter_synced_products(
        self, datahub_product_directories: list[DirectoryFile], last_synced_datetime: datetime, dir_key: str
    ) -> list[DirectoryFile]:
        """Filters out previously synced products from a list of product directories."""
        if last_synced_datetime and dir_key:
            filtered_directories = [
                product_directory
                for product_directory in datahub_product_directories
                if product_directory.last_modified.replace(tzinfo=timezone.utc) >= last_synced_datetime
                and product_directory.key != dir_key
            ]
            return filtered_directories
        else:
            return datahub_product_directories

    def _collect_category_paths_for_product(
        self, categories: list[Category], target_umid: str, current_path=None
    ) -> list[list[Category]]:
        """Recursively collect all root-to-leaf category paths for a product."""
        if current_path is None:
            current_path = []
        paths: list[list[Category]] = []
        if not categories:
            return paths
        for cat in categories:
            path = current_path + [cat]
            # If this category contains the product, and it's a leaf, add the path
            found_here = cat.products and any(p.umid == target_umid for p in cat.products)
            if found_here and (
                not cat.sub_categories
                or not any(self._collect_category_paths_for_product(cat.sub_categories, target_umid))
            ):
                paths.append(path)
            # Always search subcategories
            if cat.sub_categories:
                paths.extend(self._collect_category_paths_for_product(cat.sub_categories, target_umid, path))
        return paths

    def _collect_all_product_umids(self, categories: list[Category] | None) -> set[str]:
        """
        Recursively collect all unique product UMIDs from the given categories.

        Returns a set to ensure UMIDs are unique and to allow fast insertions,
        since products may appear in multiple categories or subcategories.
        """
        umids: set[str] = set()
        if not categories:
            return umids
        for cat in categories:
            if cat.products:
                for product in cat.products:
                    umids.add(product.umid)
            if cat.sub_categories:
                umids.update(self._collect_all_product_umids(cat.sub_categories))
        return umids
