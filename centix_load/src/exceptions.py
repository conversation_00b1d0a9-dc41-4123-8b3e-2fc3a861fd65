from typing import Any


class CentixLoadException(Exception):
    """
    Base exception for Centix Load app.
    All custom exceptions should inherit from this class.
    """

    def __init__(self, message: str = "An error occurred", *args: Any, **kwargs: dict[str, Any] | None) -> None:
        super().__init__(message, *args)
        self.message = message
        self.details = kwargs

    def __str__(self) -> str:
        if self.details:
            return f"{self.message} | Details: {self.details}"
        return self.message


class UnAuthorizedException(CentixLoadException):
    """Unauthorized Exception."""


class TooManyObjectsException(CentixLoadException):
    """Too many products found exception."""
