from uuid import UUID

from pydantic import BaseModel
from pydantic import Field


class Category(BaseModel):
    id: int = Field(..., description="WooCommerce category ID")
    guid: UUID = Field(description="Domain-specific unique identifier (GUID)")
    name: str
    parent_id: int
    slug: str
    description: str | None = None
    menu_order: int
    language: str | None = Field(default=None, description="Polylang language code")
    translations: dict[str, int] = Field(default_factory=dict)
