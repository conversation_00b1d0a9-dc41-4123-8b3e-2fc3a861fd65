from datetime import date

from humps import pascalize
from pydantic import BaseModel as PydanticBaseModel
from pydantic import ConfigDict, Field

from src.shared.enum import LanguageEnum


class BaseModel(PydanticBaseModel):
    model_config = ConfigDict(from_attributes=True, alias_generator=pascalize, populate_by_name=True)


class Id(BaseModel):
    id: int | None = None


class Language(BaseModel):
    code: LanguageEnum


class LanguageValueTranslation(BaseModel):
    language: LanguageEnum
    value: str


class LanguageNameTranslation(BaseModel):
    language: LanguageEnum
    name: str


class LanguageLabelTranslation(BaseModel):
    language: LanguageEnum
    label: str


class LanguageContentTranslation(BaseModel):
    language: LanguageEnum
    content: str


class MarketLanguage(BaseModel):
    language_code: LanguageEnum | None = None


class Market(BaseModel):
    languages: list[MarketLanguage] | None = None
    name: str | None = None


class ProductHeader(BaseModel):
    markets: list[Market] | None = None
    default_language: str | None = None
    owner: str | None = None


class Brand(Id, BaseModel):
    brand_name: str = ""
    brand_type: str | None = None
    gbin: str | None = None
    owner_name: str | None = None
    owner_gln: str | None = None
    reference: str | None = None


class Classification(Id, BaseModel):
    translations: list[LanguageNameTranslation]
    material_type: str | None = None


class HierarchyLevel(Id, BaseModel):
    translations: list[LanguageNameTranslation]


class ProductTextTranslation(LanguageLabelTranslation):
    value: str | None = None


class ProductText(Id, BaseModel):
    translations: list[ProductTextTranslation]
    is_derived: bool

    def get_translation(self, language: LanguageEnum) -> str | None:
        return next((translation.value for translation in self.translations if translation.language == language), None)


class AttributeGroup(Id, BaseModel):
    translations: list[LanguageNameTranslation]


class ProductName(BaseModel):
    translations: list[LanguageValueTranslation]

    def get_translation(self, language: LanguageEnum) -> str | None:
        return next((translation.value for translation in self.translations if translation.language == language), None)


class Translations(BaseModel):
    """Base model for storing translations"""

    translations: list[LanguageLabelTranslation] = []

    def get_translation(self, language: LanguageEnum) -> str | None:
        return next((translation.label for translation in self.translations if translation.language == language), None)


class AssetRole(Id, Translations, BaseModel):
    pass


class AssetType(AssetRole, BaseModel):
    label: str


class AsssetSubType(Id, BaseModel):
    singular: Translations = Translations()
    plural: Translations = Translations()

    def get_translation(self, language: LanguageEnum) -> str | None:
        return next(
            (translation.label for translation in self.singular.translations if translation.language == language), None
        )


class ProductAsset(Id, BaseModel):
    udai: str = Field(alias="UDAI")
    role_id: int = Field(alias="RoleId")

    def __hash__(self):
        return hash(self.udai)

    def __eq__(self, other):
        if isinstance(other, ProductAsset):
            return self.udai == other.udai and self.role_id == other.role_id
        return False


class ProductAssets(BaseModel):
    roles: list[AssetRole] = []
    types: list[AssetType] = []
    sub_types: list[AsssetSubType] = []
    asset: list[ProductAsset] = []

    def get_asset_id_for_role(self, role_id: int) -> ProductAsset | None:
        return next((asset for asset in self.asset if asset.role_id == role_id), None)


class Translation(BaseModel):
    language: str
    name: str
    content: str


class AttributeTranslation(BaseModel):
    language: str
    name: str
    content: str
    attribute_group_id: int


class SingleAttributeTranslation(BaseModel):
    language: str
    name: str


class AttributeUnitType(Id, BaseModel):
    unit_before: bool | None = None
    translations: list[LanguageNameTranslation] = []


class AttributeValue(BaseModel):
    unit_id: int | None = None
    content: str = ""
    is_derived: bool | None = None
    translations: list[LanguageContentTranslation] = []

    def get_translation(self, language: LanguageEnum) -> str | None:
        return next(
            (translation.content for translation in self.translations if translation.language == language), None
        )


class Attribute(Id, BaseModel):
    translations: list[SingleAttributeTranslation]
    values: list[AttributeValue] = []
    type: str
    multi_lingual_values: bool | None = None
    is_read_only: bool | None = None
    attribute_group_id: int


class ProductAttributes(BaseModel):
    units: list[AttributeUnitType] = []
    attribute: list[Attribute]


class SegmentationTargetGroup(Id, BaseModel):
    name: str
    from_date: date
    until_date: date


class SegmentationMarket(Id, BaseModel):
    languages: list[MarketLanguage] = []
    name: str


class Segmentation(BaseModel):
    target_groups: list[SegmentationTargetGroup] = []
    markets: list[SegmentationMarket] = []


class Product(Id, BaseModel):
    """Product model"""

    umid: str = Field(alias="UMID")
    article_number: str
    master_id: int | None = None
    languages: list[Language] = []
    header: ProductHeader | None = None
    brand: Brand
    classification: Classification
    hierarchy_level: HierarchyLevel | None = None
    name: ProductName
    master_name: ProductName | None = None
    assets: ProductAssets
    texts: list[ProductText] = []
    attribute_groups: list[AttributeGroup]
    attributes: ProductAttributes
    segmentation: Segmentation
