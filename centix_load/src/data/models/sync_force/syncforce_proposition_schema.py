from datetime import datetime
from typing import Any

from humps import camelize
from pydantic import BaseModel as PydanticBaseModel
from pydantic import ConfigDict


class BaseModel(PydanticBaseModel):
    model_config = ConfigDict(from_attributes=True, alias_generator=camelize, populate_by_name=True)


# Translation model
class Translation(BaseModel):
    language: str
    label: str


# Title model
class Title(BaseModel):
    translations: list[Translation]


# Language model
class Language(BaseModel):
    language: str


# Market model
class Market(BaseModel):
    id: int
    name: str
    reference: str | None = None
    languages: list[Language]


# Text Role model
class TextRole(BaseModel):
    id: int
    sort_index: int
    bref: str | None = None
    translations: list[Translation]


# Digital Asset Role model
class DigitalAssetRole(BaseModel):
    id: int
    sort_index: int
    bref: str | None = None
    translations: list[Translation]


# Product model
class ProductItem(BaseModel):
    umid: str
    pmid: str
    change_date: datetime
    digital_life_cycle_status: str
    sort_index: int
    star: bool


# Content model
class Content(BaseModel):
    id: int
    role_id: int
    translations: list[Translation]


# Category model
class Category(BaseModel):
    guid: str
    id: int
    sort_index: int
    translations: list[Translation]
    products: list[ProductItem] | None
    contents: list[Content] | None = None
    digital_assets: Any | None = None
    related_categories: Any | None = None
    categories: Any | None = None


# Main Proposition Model
class Proposition(BaseModel):
    guid: str
    reference: str
    title: Title
    markets: list[Market]
    text_roles: list[TextRole]
    digital_asset_roles: list[DigitalAssetRole]
    categories: list[Category]
