from datetime import datetime
from typing import Any
from typing import List
from typing import Optional

from humps import camelize
from pydantic import BaseModel as PydanticBaseModel
from pydantic import ConfigDict


class BaseModel(PydanticBaseModel):
    model_config = ConfigDict(from_attributes=True, alias_generator=camelize, populate_by_name=True)


# Translation model
class Translation(BaseModel):
    language: str
    label: str


# Title model
class Title(BaseModel):
    translations: List[Translation]


# Language model
class Language(BaseModel):
    language: str


# Market model
class Market(BaseModel):
    id: int
    name: str
    reference: Optional[str] = None
    languages: List[Language]


# Text Role model
class TextRole(BaseModel):
    id: int
    sort_index: int
    bref: str | None = None
    translations: List[Translation]


# Digital Asset Role model
class DigitalAssetRole(BaseModel):
    id: int
    sort_index: int
    bref: str | None = None
    translations: List[Translation]


# Product model
class ProductItem(BaseModel):
    umid: str
    pmid: str
    change_date: datetime
    digital_life_cycle_status: str
    sort_index: int
    star: bool


# Content model
class Content(BaseModel):
    id: int
    role_id: int
    translations: List[Translation]


# Category model
class Category(BaseModel):
    guid: str
    id: int
    sort_index: int
    translations: List[Translation]
    products: List[ProductItem] | None
    contents: Optional[List[Content]] = None
    digital_assets: Optional[Any] = None
    related_categories: Optional[Any] = None
    categories: Optional[Any] = None


# Main Proposition Model
class Proposition(BaseModel):
    guid: str
    reference: str
    title: Title
    markets: List[Market]
    text_roles: List[TextRole]
    digital_asset_roles: List[DigitalAssetRole]
    categories: List[Category]
