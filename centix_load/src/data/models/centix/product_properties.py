import logging

from humps import pascalize
from pydantic import BaseModel, ConfigDict, Field, field_validator


class ProductProperties(BaseModel):
    model_config = ConfigDict(from_attributes=True, alias_generator=pascalize, populate_by_name=True)

    max_working_pressure_bar: float | None = None
    max_working_pressure_psi: float | None = Field(alias="MaxWorkingPressurePSI", default=None)
    hip_classification: int | None = Field(alias="HIPClassification", default=None)
    model: str | None = None
    service_brand: str | None = None
    battery_included: bool | None = None

    @field_validator("model")
    def truncate_model(cls, value: str | None) -> str | None:
        if value and len(value) > 50:
            logging.warning(f"Model of length {len(value)} too long, reducing to the first 50 characters")
            return value[:50]
        return value
