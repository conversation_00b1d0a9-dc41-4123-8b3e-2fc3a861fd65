import logging
from datetime import datetime, <PERSON><PERSON><PERSON>
from uuid import UUID

from cachetools import cached
from centix_api_client import DocumentKind<PERSON>pi
from centix_api_client.api import DocumentsApi, ProductsApi

from src.data.mappers.centix_document_kind_mapper import DocumentKindMapper
from src.data.mappers.centix_document_mapper import DocumentMapper
from src.domain.models.asset import AssetDetails, File
from src.domain.models.document import Document
from src.domain.models.document_kind import DocumentKind
from src.domain.models.product import Product, ProductAsset
from src.exceptions import CentixLoadException
from src.shared.enum import AssetRoleIds, AssetSubTypeIds, LanguageEnum
from src.shared.env import Env
from src.shared.utils.api_client_wrapper import ApiClientWrapper

# Hardcoded here as it is unlikely to ever change.
RESCUE_PRODUCT_GROUP_ID = "f5f8c74d-bd48-4c77-9f1d-4949f6e565da"
INDUSTRY_PRODUCT_GROUP_ID = "9c308035-7925-431a-9a1e-df65de1b0327"

SUPPORTED_LANGUAGES = [LanguageEnum.EN, LanguageEnum.DE, LanguageEnum.NL]
SUPPORTED_SUBTYPES = [
    AssetSubTypeIds.SERVICE_DOCUMENT,
    AssetSubTypeIds.SERVICE_BULLETIN,
    AssetSubTypeIds.TECHNICAL_SPECIFICATION_SHEET,
    AssetSubTypeIds.USER_MANUAL,
]


class CentixDocumentDatasource:
    """Wrapper around Centix API client to manage documents."""

    def __init__(self, centix_api_client: ApiClientWrapper, settings: Env):
        self._document_client = DocumentsApi(centix_api_client)
        self._product_client = ProductsApi(centix_api_client)
        self._document_kind = DocumentKindApi(centix_api_client)
        self._settings = settings

    @cached(cache={})
    def get_document_kind_by_id(self, kind_id: str) -> DocumentKind:
        """Product DocumentKinds are managed in Centix, the requested type should be expected to exist"""
        document_kinds = self._document_kind.document_kind_get_list()
        for document_kind in document_kinds:
            if document_kind.id == kind_id:
                return DocumentKindMapper.to_domain(document_kind)
        raise CentixLoadException(f"No DocumentKind found with id {kind_id}")

    def get_document_by_unique_id(self, id: str) -> Document | None:
        """Retrieve a Centix document by its unique ID."""
        response = self._document_client.documents_o_data_list(filter=f"ID eq '{id}'")
        if response.items:
            return DocumentMapper.to_domain(response.items[0])
        return None

    def create_document(self, document: Document) -> Document:
        """Create a new document in Centix."""
        centix_dto = DocumentMapper.to_centix_create_document_from_url(document)
        centix_document = self._document_client.documents_create(centix_dto)
        return DocumentMapper.to_domain(centix_document)

    def delete_document_by_aid(self, document_aid: int) -> None:
        """Delete a document by AutoID."""
        self._document_client.documents_delete(autoid=document_aid)

    def link_document_to_product(self, product_id: int, document_id: int) -> None:
        """Link a document to a product."""
        self._product_client.sales_products_link_document(product_id, document_id)

    def get_product_documents(self, product_id: int) -> list[Document]:
        """Retrieve all documents linked to a product."""
        response = self._product_client.sales_products_o_data_list_documents(product_id)
        return [DocumentMapper.to_domain(doc) for doc in response.items]

    def upsert(self, product_auto_id: int, product: Product, assets: list[AssetDetails]) -> None:
        """Upsert a document to Centix."""
        existing_documents = self.get_product_documents(product_auto_id)
        updated_documents = []

        for asset in assets:
            if not self._is_supported_asset(asset):
                continue

            try:
                product_asset = next(a for a in product.assets.asset if a.udai == asset.udai)
            except StopIteration:
                logging.warning(f"No ProductAsset found for asset {asset.udai}, skipping.")
                continue

            document_kind = self._collect_document_kind_by_role(product_asset)
            if document_kind is None:
                logging.info(f"No document kind found for asset {asset.udai}, not uploading to centix")
                continue

            # Handle technical specification sheet separately
            document_kind = self._tech_spec_sheet_exception(asset, document_kind, product.leaf_category_guids)
            updated_documents.extend(
                self._process_document_for_languages(asset, document_kind, product, existing_documents, product_auto_id)
            )

        self._remove_unused_documents(product_auto_id, updated_documents)

    def _is_supported_asset(self, asset: AssetDetails) -> bool:
        """Check if asset is supported based on its sub_type_id."""
        if asset.sub_type_id not in SUPPORTED_SUBTYPES:
            logging.debug(f"Asset {asset.udai} with sub type {asset.sub_type_id} is not a document for Centix")
            return False
        return True

    def _collect_document_kind_by_role(self, product_asset: ProductAsset) -> DocumentKind | None:
        """Collects the document kind for an asset based on it role in the Product schema."""
        logging.info(f"Collecting document kind for asset {product_asset.udai} with role {product_asset.role_id}")
        match product_asset.role_id:
            case AssetRoleIds.PUBLIC_MEDIA.value:
                return self.get_document_kind_by_id("PM")
            case AssetRoleIds.DEALER_MEDIA.value:
                return self.get_document_kind_by_id("DM")
            case AssetRoleIds.SPEC_SHEET.value:
                # Temporary value for the spec sheet, because of the Exception that those documents have
                return self.get_document_kind_by_id("DM")
            case _:
                return None

    def _tech_spec_sheet_exception(
        self, asset: AssetDetails, document_kind: DocumentKind, category_guids: list[UUID]
    ) -> DocumentKind:
        """
        Handles the specific exception where Technical Specification Sheets
        for Rescue products follow Rule 5 correctly.
        """
        # If not a Technical Specification Sheet, return original kind
        if asset.sub_type_id != AssetSubTypeIds.TECHNICAL_SPECIFICATION_SHEET:
            logging.debug(f"Not a Technical Specification Sheet for {asset.udai}, final kind: {document_kind.id}")
            return document_kind

        if RESCUE_PRODUCT_GROUP_ID in category_guids:
            logging.debug(f"Applied Tech Spec exception for {asset.udai}, final kind: {document_kind.id}")
            return self.get_document_kind_by_id("DM")

        if INDUSTRY_PRODUCT_GROUP_ID in category_guids:
            logging.debug(f"Applied Tech Spec exception for {asset.udai}, final kind: {document_kind.id}")
            return self.get_document_kind_by_id("PM")

        return document_kind

    def _process_document_for_languages(
        self,
        asset: AssetDetails,
        document_kind: DocumentKind,
        product: Product,
        existing_documents: list[Document],
        product_auto_id: int,
    ) -> list[Document]:
        """Process and upload documents for supported languages."""
        updated_documents = []
        for language in SUPPORTED_LANGUAGES:
            file, char = self._get_document_file(asset, language)
            if not file:
                logging.debug(f"No document found for language {language.name}")
                continue

            if language != LanguageEnum.EN and file == asset.get_file_for_char_and_language(char, LanguageEnum.EN):
                logging.debug(f"Skipping {language} document, as it is the same as the English document")
                continue

            document = DocumentMapper.create_document_from_assets(
                file, asset, product, file.size, language, document_kind
            )
            updated_documents.append(
                self.upsert_document_from_url(
                    product_aid=product_auto_id,
                    asset=asset,
                    document=document,
                    product_documents=existing_documents,
                )
            )
        return updated_documents

    def _get_document_file(self, asset: AssetDetails, language: LanguageEnum) -> tuple[File | None, str]:
        """Get the document file for the given language."""
        char = "G"
        file = asset.get_file_for_char_and_language(char, language)
        if not file:
            logging.debug("Trying low resolution...")
            char = "F"
            file = asset.get_file_for_char_and_language(char, language)
        return file, char

    def upsert_document_from_url(
        self,
        product_aid: int,
        asset: AssetDetails,
        document: Document,
        product_documents: list[Document],
    ) -> Document:
        """Uploads Centix document from URL."""
        try:
            logging.info(f"Starting upsert for document: {document.id}")

            # Attempt to find an existing document
            existing_document = self._find_existing_document(product_documents or [], document)
            if existing_document:
                return self._handle_existing_document(product_aid, asset, document, existing_document)

            # Check for document by ID if not found
            document_by_id = self._get_document_by_id(document)
            if document_by_id:
                return self._handle_existing_document_linked(product_aid, asset, document, document_by_id)

            # Create new document if no existing one found
            logging.info(f"Creating new document: {document.id}")
            return self._handle_new_document(product_aid, document)

        except Exception as e:
            logging.error(f"Failed to upsert document from URL. {e}")
            raise

    def _find_existing_document(
        self,
        documents: list[Document],
        document: Document,
    ) -> Document | None:
        """Finds an existing document that matches the given Document."""
        try:
            for doc in documents:
                if doc.id == document.id:
                    return doc
            return None
        except Exception as e:
            logging.error(f"Failed to find existing document. {e}")
            raise

    def _handle_existing_document(
        self,
        product_aid: int,
        asset: AssetDetails,
        document: Document,
        existing_document: Document,
    ) -> Document:
        """Handles the case where the document already exists."""
        return self._update_document(product_aid, asset, document, existing_document)

    def _get_document_by_id(self, document: Document) -> Document | None:
        """Fetch document by its unique ID if available."""
        if document.id:
            return self.get_document_by_unique_id(document.id)
        return None

    def _handle_existing_document_linked(
        self,
        product_aid: int,
        asset: AssetDetails,
        document: Document,
        document_by_id: Document,
    ) -> Document:
        """Handles the case where the document exists but is not linked."""
        logging.info("Found existing document not linked to this product")
        if document_by_id.auto_id is None:
            raise ValueError(f"Cannot link document {document_by_id.id} to product {product_aid}: auto_id is None")

        self.link_document_to_product(product_aid, document_by_id.auto_id)
        return self._update_document(product_aid, asset, document, document_by_id)

    def _update_document(
        self,
        product_aid: int,
        asset: AssetDetails,
        new_document: Document,
        existing_document: Document,
    ) -> Document:
        if self._settings.centix_force_update_documents:
            logging.info(f"Force update for document: {existing_document.id}")
            return self._replace_document(product_aid, existing_document, new_document)

        if not self._check_document_timestamp(existing_document, asset):
            logging.info(f"Document {existing_document.id} is up-to-date.")
            return existing_document
        else:
            logging.info(f"Replacing document: {existing_document.id}")
            return self._replace_document(product_aid, existing_document, new_document)

    def _handle_new_document(self, product_aid: int, document: Document) -> Document:
        """Handles the case where a new document needs to be created."""
        document = self.create_document(document)
        if not isinstance(document, Document):
            raise TypeError(f"Expected Document, got {type(document)}")

        if document.auto_id:
            logging.info(f"Linking document {document.auto_id} to product {product_aid}")
            self.link_document_to_product(product_aid, document.auto_id)
        else:
            raise ValueError("Document auto_id is None")

        return document

    def _replace_document(
        self,
        product_aid: int,
        existing_document: Document,
        document: Document,
    ) -> Document:
        """Replace document in Centix."""
        if existing_document.auto_id is None:
            raise ValueError("Document auto_id is None")
        logging.debug(f"Deleting document: {existing_document.auto_id}")
        # delete document
        self.delete_document_by_aid(existing_document.auto_id)
        # create new document
        logging.debug(f"Creating new document: {document.id}")
        new_doc = self.create_document(document)
        # link document to product
        logging.debug(f"Linking document {new_doc.auto_id} to product {product_aid}")
        if new_doc.auto_id is None:
            raise ValueError("Document auto_id is None")
        self.link_document_to_product(product_aid, new_doc.auto_id)
        return new_doc

    def _check_document_timestamp(self, document: Document, asset: AssetDetails) -> bool:
        """Checks if the syncforce asset has recent changes compared to the Centix document."""
        if document.time_stamp is None or not isinstance(document.time_stamp, str):
            return True

        try:
            d_timestamp = datetime.strptime(document.time_stamp[:19], "%Y-%m-%dT%H:%M:%S")

            if not d_timestamp:
                return True

            return (asset.change_date - d_timestamp) >= timedelta(
                hours=2
            )  # minimal 2hrs diff to account for potential timezone differences
        except (ValueError, TypeError) as e:
            # Handle any parsing errors
            logging.warning(f"Failed to parse timestamp {document.time_stamp}: {e}")
            return True  # If we can't parse the timestamp, consider it outdated

    def _remove_unused_documents(self, product_auto_id: int, updated_documents: list[Document]) -> None:
        """Remove documents from Centix that are no longer updated."""
        existing_documents = self.get_product_documents(product_auto_id)
        for document in existing_documents:
            if not any(doc.id == document.id for doc in updated_documents):
                logging.info(f"Removing document {document.id}")
                if document.auto_id is None:
                    raise ValueError("Document auto_id is None")
                self.delete_document_by_aid(document.auto_id)
