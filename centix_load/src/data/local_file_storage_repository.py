import json
import logging
import os

from src.domain.models.sync_config import SyncConfig
from src.domain.models.sync_metadata.sync_metadata_directory_file import SyncMetaDataDirectoryFile
from src.domain.repositories.local_storage_repository import AbstractFileStorageRepository
from src.shared.env import Env


class LocalFileStorageRepository(AbstractFileStorageRepository):
    """
    Repository to manage the local file storage. This repository is used to store the sync metadata
    file in the local file system. Which is used to keep track of the last sync date and time.
    """

    def __init__(self, env: Env):
        self.logger = logging.getLogger(__name__)
        self.sync_meta_data_file_path = env.mounted_files_path

    def _sync_metadata_filename(self, sync_config: SyncConfig) -> str:
        return f"sync_metadata-{sync_config.sync_type.value}.json"

    def write_sync_metadata(self, sync_metadata: SyncMetaDataDirectoryFile, sync_config: SyncConfig) -> None:
        """
        Write the sync metadata to the local file system.
        """
        try:
            sync_metadata_dict = sync_metadata.model_dump()
            file_path = os.path.join(self.sync_meta_data_file_path, self._sync_metadata_filename(sync_config))
            with open(file_path, "w") as file:
                file.write(json.dumps(sync_metadata_dict, default=str))
        except Exception as e:
            raise Exception(f"Failed to write sync metadata to file. Reason: {e}")

    def read_sync_metadata(self, sync_config) -> SyncMetaDataDirectoryFile:
        """
        Read the sync metadata from the local file system.
        """
        try:
            file_path = os.path.join(self.sync_meta_data_file_path, self._sync_metadata_filename(sync_config))
            with open(file_path, "r") as file:
                sync_metadata_dict = json.loads(file.read())
            return SyncMetaDataDirectoryFile(
                last_synced=sync_metadata_dict["last_synced"], key=sync_metadata_dict["key"]
            )
        except Exception as e:
            raise Exception(f"Failed to read sync metadata from file. Reason: {e}")
