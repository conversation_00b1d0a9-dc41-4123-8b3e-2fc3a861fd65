import logging

from woocommerce import API

from src.data.models.woocommerce.category import Category as WCCategory


class WoocommerceCategoryDatasource:
    def __init__(self, woocommerce_api: API):
        self._woocommerce_api = woocommerce_api

    def get_categories(self) -> list[WCCategory]:
        """
        Get all categories from all languages.
        Polylang returns categories from all languages by default.
        """
        categories = []
        page = 1
        while True:
            response = self._woocommerce_api.get("products/categories", params={"per_page": 100, "page": page})
            response.raise_for_status()
            data = response.json()
            categories.extend([self._map_response_category(cat) for cat in data])
            if str(page) >= response.headers.get("X-WP-TotalPages", "1"):
                break
            page += 1
        return categories

    def create(self, payload: dict) -> WCCategory:
        """Create a category in the default language (English)."""
        response = self._woocommerce_api.post("products/categories", payload)
        response.raise_for_status()
        data = response.json()
        return self._map_response_category(data)

    def create_translation(
        self, payload: dict, language: str, source_category_id: int, source_language_code: str = "en"
    ) -> WCCategory:
        """
        Creates a category translation using the official Polylang two-step process.

        Args:
            payload: Category data (name, slug, parent, etc.)
            language: Language code for the new translation (e.g., 'de', 'fr')
            source_category_id: ID of the main language category this is a translation of.
            source_language_code: Language code of the main category (defaults to 'en').
        """
        # Step 1: Create the new category with its language assigned.
        # The payload should NOT contain any translation info at this stage.
        creation_payload = {**payload, "lang": language}

        create_response = self._woocommerce_api.post("products/categories", creation_payload)
        create_response.raise_for_status()
        new_category = create_response.json()
        new_category_id = new_category["id"]

        logging.info(f"Step 1 successful: Created {language} category with ID {new_category_id}.")

        # Step 2: Update the newly created category to link it to the source.
        # This uses a POST request on the specific category ID with URL parameters.
        update_path = f"products/categories/{new_category_id}"
        link_params = {f"translations[{source_language_code}]": source_category_id}

        update_response = self._woocommerce_api.post(update_path, data={}, params=link_params)
        update_response.raise_for_status()

        logging.info(f"Step 2 successful: Linked category {new_category_id} to source {source_category_id}.")

        # Return the final, linked category
        return self._map_response_category(update_response.json())

    def update(self, category_id: int, payload: dict) -> WCCategory:
        response = self._woocommerce_api.put(f"products/categories/{category_id}", payload)
        response.raise_for_status()
        data = response.json()
        return self._map_response_category(data)

    def delete(self, category_id: int) -> None:
        """
        Delete a category in WooCommerce by its ID.
        """
        response = self._woocommerce_api.delete(f"products/categories/{category_id}", params={"force": True})
        response.raise_for_status()

    @staticmethod
    def _map_response_category(category: dict) -> WCCategory:
        lang = category.get("lang") or None

        # Get the translations, defaulting to an empty dict if missing or not a dict
        translations_data = category.get("translations", {})
        translations = translations_data if isinstance(translations_data, dict) else {}

        return WCCategory(
            id=category["id"],
            guid=category.get("guid"),
            name=category["name"],
            slug=category["slug"],
            parent_id=category["parent"],
            description=category.get("description"),
            menu_order=category.get("menu_order") or 0,
            language=lang,
            translations=translations,
        )
