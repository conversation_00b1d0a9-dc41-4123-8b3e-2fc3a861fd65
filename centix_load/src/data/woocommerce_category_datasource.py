from woocommerce import API

from src.data.models.woocommerce.category import Category as WCCategory


class WoocommerceCategoryDatasource:
    def __init__(self, woocommerce_api: API):
        self._woocommerce_api = woocommerce_api

    def get_categories(self) -> list[WCCategory]:
        categories = []
        page = 1
        while True:
            response = self._woocommerce_api.get("products/categories", params={"per_page": 100, "page": page})
            response.raise_for_status()
            data = response.json()
            categories.extend([self._map_response_category(cat) for cat in data])
            if str(page) >= response.headers.get("X-WP-TotalPages", "1"):
                break
            page += 1
        return categories

    def create(self, payload: dict) -> WCCategory:
        response = self._woocommerce_api.post("products/categories", payload)
        response.raise_for_status()
        data = response.json()
        return self._map_response_category(data)

    def update(self, category_id: int, payload: dict) -> WCCategory:
        response = self._woocommerce_api.put(f"products/categories/{category_id}", payload)
        response.raise_for_status()
        data = response.json()
        return self._map_response_category(data)

    def delete(self, category_id: int) -> None:
        """
        Delete a category in WooCommerce by its ID.
        """
        response = self._woocommerce_api.delete(f"products/categories/{category_id}", params={"force": True})
        response.raise_for_status()

    def _map_response_category(self, category: dict) -> WCCategory:
        return WCCategory(
            id=category["id"],
            guid=category.get("guid") or None,
            name=category["name"],
            slug=category["slug"],
            parent_id=category["parent"],
            description=category.get("description"),
            menu_order=category.get("menu_order") or 0,
        )
