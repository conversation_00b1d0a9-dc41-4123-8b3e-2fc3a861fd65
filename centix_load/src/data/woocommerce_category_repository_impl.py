import html
import logging
from typing import De<PERSON><PERSON><PERSON><PERSON>
from typing import Dict
from uuid import UUID

from woocommerce import API

from src.data.category_sync_helper import CategorySyncHelper
from src.data.models.woocommerce.category import Category as WCCategory
from src.data.woocommerce_category_datasource import WoocommerceCategoryDatasource
from src.domain.models.proposition import Category
from src.domain.models.proposition import CategoryTranslation
from src.domain.repositories.target_category_repository import TargetCategoryRepository
from src.shared.enum import LanguageEnum


class WoocommerceCategoryRepositoryImpl(TargetCategoryRepository):
    """
    Repository implementation for syncing categories with WooCommerce using Polylang translations.
    Handles upserting categories in multiple languages and ensures slug uniqueness among siblings.
    English is treated as the main language, with other languages created as translations.
    """

    def __init__(self, woocommerce_api: API, woocommerce_category_datasource: WoocommerceCategoryDatasource):
        """
        Initialize the repository with a WooCommerce API client.
        """
        self._woocommerce_api = woocommerce_api
        self._category_datasource = woocommerce_category_datasource
        self._not_supported_languages = [LanguageEnum.PL.value, LanguageEnum.RU.value, LanguageEnum.ZH.value]

    def upsert(self, categories: list[Category]) -> None:
        """
        Upsert (create or update) a list of categories in WooCommerce with Polylang translations.
        Orchestrates syncing, delegating English and translation handling to helpers.
        """
        existing_categories = self._category_datasource.get_categories()
        existing_by_lang_guid = CategorySyncHelper.group_existing_by_language_and_guid(existing_categories)
        used_slugs_by_parent_lang = CategorySyncHelper.build_used_slugs_by_parent_and_language(existing_categories)
        processed_guids = set[UUID]()

        english_category_ids = self._upsert_english_categories(
            categories, existing_by_lang_guid, used_slugs_by_parent_lang, processed_guids
        )
        self._upsert_translation_categories(
            categories, existing_by_lang_guid, used_slugs_by_parent_lang, processed_guids, english_category_ids
        )
        self._delete_untracked_categories(existing_categories, processed_guids)

    def _upsert_english_categories(
        self,
        categories: list[Category],
        existing_by_lang_guid: Dict[str, Dict[UUID, WCCategory]],
        used_slugs_by_parent_lang: DefaultDict[str, DefaultDict[int, set[str]]],
        processed_guids: set,
    ) -> Dict[str, int]:
        """
        Create or update English categories, return mapping of guid to WooCommerce ID.
        """
        english_category_ids = {}
        english_label_value = LanguageEnum.EN.value
        for category, parent in CategorySyncHelper.flatten_categories(categories):
            english_label = CategorySyncHelper.get_label_for_language(category, english_label_value)
            parent_guid = parent.guid if parent else None
            parent_id = CategorySyncHelper.get_parent_id_for_language(
                parent_guid, existing_by_lang_guid, english_label_value
            )
            existing_english_wc = existing_by_lang_guid.get(english_label_value, {}).get(UUID(category.guid))
            if existing_english_wc:
                self._update_category_for_language(
                    existing_english_wc, category, english_label, parent_id, english_label_value
                )
                english_category_ids[category.guid] = existing_english_wc.id
                processed_guids.add(existing_english_wc.guid)
            else:
                wc_category = self._create_english_category(
                    category, english_label, parent_id, used_slugs_by_parent_lang, existing_by_lang_guid
                )
                english_category_ids[category.guid] = wc_category.id
                processed_guids.add(wc_category.guid)
        return english_category_ids

    def _upsert_translation_categories(
        self,
        categories: list[Category],
        existing_by_lang_guid: Dict[str, Dict[UUID, WCCategory]],
        used_slugs_by_parent_lang: DefaultDict[str, DefaultDict[int, set[str]]],
        processed_guids: set[UUID],
        english_category_ids: Dict[str, int],
    ) -> None:
        """
        Create or update translation categories for all supported languages except English.
        """
        for category, parent in CategorySyncHelper.flatten_categories(categories):
            for translation in category.translations:
                if translation.language in [LanguageEnum.EN.value] + self._not_supported_languages:
                    continue
                parent_guid = parent.guid if parent else None
                parent_id = CategorySyncHelper.get_parent_id_for_language(
                    parent_guid, existing_by_lang_guid, translation.language
                )
                translation_of_id = english_category_ids.get(category.guid)
                if not translation_of_id:
                    logging.warning(
                        f"No English category found for {category.guid}, skipping {translation.language} translation"
                    )
                    continue
                existing_translation_wc = existing_by_lang_guid.get(translation.language, {}).get(UUID(category.guid))
                if existing_translation_wc:
                    self._update_category_for_language(
                        existing_translation_wc, category, translation.label, parent_id, translation.language
                    )
                    processed_guids.add(existing_translation_wc.guid)
                else:
                    wc_category = self._create_translation_category(
                        category,
                        translation,
                        parent_id,
                        translation_of_id,
                        used_slugs_by_parent_lang,
                        existing_by_lang_guid,
                    )
                    processed_guids.add(wc_category.guid)

    def _delete_untracked_categories(self, existing_categories: list[WCCategory], processed_guids: set) -> None:
        """
        Delete categories not present in processed_guids, skipping WooCommerce's default category.
        """
        for wc in existing_categories:
            is_default = wc.id == 1 or wc.slug == "uncategorized"
            is_untracked = wc.guid is None or wc.guid not in processed_guids
            if is_untracked and not is_default:
                logging.info(f"Deleting category {wc.guid or wc.name} (not present in sync)")
                self._category_datasource.delete(wc.id)

    def _create_english_category(
        self,
        category: Category,
        label: str,
        parent_id: int,
        used_slugs_by_parent_lang: DefaultDict[str, DefaultDict[int, set[str]]],
        existing_by_lang_guid: Dict[str, Dict[UUID, WCCategory]],
    ) -> WCCategory:
        """
        Create a new English category in WooCommerce (main language).
        Returns the created WCCategory.
        """
        english_label_value = LanguageEnum.EN.value
        used_slugs = used_slugs_by_parent_lang[english_label_value][parent_id]
        slug = CategorySyncHelper.generate_unique_slug_for_language(category, english_label_value, used_slugs)
        used_slugs.add(slug)

        logging.info(f"Creating English category {category.guid} with parent {parent_id}")
        payload = {
            "name": label,
            "slug": slug,
            "parent": parent_id,
            "guid": category.guid,
            "menu_order": category.sort_index,
        }
        wc_category = self._category_datasource.create(payload)

        # Update tracking
        if english_label_value not in existing_by_lang_guid:
            existing_by_lang_guid[english_label_value] = {}
        existing_by_lang_guid[english_label_value][wc_category.guid] = wc_category

        return wc_category

    def _create_translation_category(
        self,
        category: Category,
        translation: CategoryTranslation,
        parent_id: int,
        translation_of_id: int,
        used_slugs_by_parent_lang: DefaultDict[str, DefaultDict[int, set[str]]],
        existing_by_lang_guid: Dict[str, Dict[UUID, WCCategory]],
    ) -> WCCategory:
        """
        Create a new translation category in WooCommerce using Polylang.
        Returns the created WCCategory.
        """
        language = translation.language
        used_slugs = used_slugs_by_parent_lang[language][parent_id]
        slug = CategorySyncHelper.generate_unique_slug_for_language(category, language, used_slugs)
        used_slugs.add(slug)

        logging.info(
            f"Creating {language} translation for category {category.guid} with parent {parent_id} "
            f"and slug {slug} with translation of {translation_of_id}"
        )
        payload = {
            "name": translation.label,
            "slug": slug,
            "parent": parent_id,
            "guid": category.guid,
            "menu_order": category.sort_index,
        }

        wc_category = self._category_datasource.create_translation(
            payload=payload,
            language=language,
            source_category_id=translation_of_id,
            source_language_code=LanguageEnum.EN.value,
        )

        # Update tracking
        if language not in existing_by_lang_guid:
            existing_by_lang_guid[language] = {}
        existing_by_lang_guid[language][wc_category.guid] = wc_category

        return wc_category

    def _update_category_for_language(
        self, existing_wc: WCCategory, category: Category, label: str, parent_id: int, language: str
    ) -> None:
        """
        Update a category in WooCommerce if its name, parent, or menu order has changed.
        Compares the HTML-unescaped label to handle cases like '&' vs '&amp;'.
        """
        # Unescape both names for comparison
        existing_name_unescaped = html.unescape(existing_wc.name)
        label_unescaped = html.unescape(label)
        if (
            existing_name_unescaped != label_unescaped
            or existing_wc.parent_id != parent_id
            or existing_wc.menu_order != category.sort_index
        ):
            payload = {
                "name": label,
                "parent": parent_id,
                "menu_order": category.sort_index,
            }
            logging.info(f"Updating {language} category {category.guid} (ID: {existing_wc.id})")
            self._category_datasource.update(existing_wc.id, payload)
        else:
            logging.info(
                f"Skipping update for {language} category {category.guid} (ID: {existing_wc.id}), no changes detected"
            )
