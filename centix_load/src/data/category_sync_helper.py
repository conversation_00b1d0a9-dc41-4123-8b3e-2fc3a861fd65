from typing import DefaultDict
from typing import Dict
from typing import Tuple
from uuid import UUID

from slugify import slugify

from src.data.models.woocommerce.category import Category as WCCategory
from src.domain.models.proposition import Category
from src.shared.enum import LanguageEnum


class CategorySyncHelper:
    @staticmethod
    def group_existing_by_language_and_guid(existing_categories: list[WCCategory]) -> Dict[str, Dict[UUID, WCCategory]]:
        grouped: Dict[str, Dict[UUID, WCCategory]] = {}
        for wc in existing_categories:
            if wc.guid is None:
                continue
            lang = wc.language or LanguageEnum.EN.value
            if lang not in grouped:
                grouped[lang] = {}
            grouped[lang][wc.guid] = wc
        return grouped

    @staticmethod
    def build_used_slugs_by_parent_and_language(
        existing_categories: list[WCCategory],
    ) -> DefaultDict[str, DefaultDict[int, set[str]]]:
        used_slugs: DefaultDict[str, DefaultDict[int, set[str]]] = DefaultDict(lambda: DefaultDict(set))
        for wc in existing_categories:
            lang = wc.language or LanguageEnum.EN.value
            used_slugs[lang][wc.parent_id].add(wc.slug)
        return used_slugs

    @staticmethod
    def flatten_categories(categories: list[Category]) -> list[Tuple[Category, Category | None]]:
        def recurse(cats, parent=None):
            for cat in cats:
                yield cat, parent
                yield from recurse(cat.sub_categories, cat)

        return list(recurse(categories))

    @staticmethod
    def get_label_for_language(category: Category, language: str) -> str:
        try:
            return next(t.label for t in category.translations if t.language == language)
        except StopIteration:
            raise ValueError(f"No {language} translation found for category {category.guid}")

    @staticmethod
    def get_parent_id_for_language(
        parent_guid: str | None, existing_by_lang_guid: Dict[str, Dict[UUID, WCCategory]], language: str
    ) -> int:
        if parent_guid:
            lang_categories = existing_by_lang_guid.get(language, {})
            parent_wc = lang_categories.get(UUID(parent_guid))
            return parent_wc.id if parent_wc else 0
        return 0

    @staticmethod
    def generate_slug_for_language(category: Category, language: str) -> str:
        label = CategorySyncHelper.get_label_for_language(category, language)
        return slugify(label)

    @staticmethod
    def generate_unique_slug_for_language(category: Category, language: str, used_slugs: set[str]) -> str:
        base_slug = CategorySyncHelper.generate_slug_for_language(category, language)
        slug = base_slug
        i = 0
        while slug in used_slugs:
            slug = f"{base_slug}-{i}"
            i += 1
        return slug
