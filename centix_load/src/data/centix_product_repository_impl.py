import logging

from cachetools import cached
from centix_api_client import (
    CentixAPIDTOContentLanguageItem,
    CentixAPIDTOCreateBrandType,
    CentixAPIDTOCreateBrandTypeDefaultMISchedule,
    CentixAPIDTOReadBrand,
    CentixAPIDTOReadBrandType,
    CentixAPIDTOReadBrandTypeDefaultMISchedule,
    CentixAPIDTOReadMI,
    CentixAPIDTOReadObjectType,
    CentixAPIDTOReadProductCategory,
    CentixAPIDTOReadProductGroup,
    CentixAPIDTOReadSalesProduct,
    CentixAPIDTOUpdateBrandType,
    CentixAPIDTOUpdateSalesProduct,
)
from centix_api_client.api import (
    BrandsA<PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ched<PERSON><PERSON>,
    BrandTypesApi,
    MIPlansApi,
    ObjectTypesApi,
    ProductCategoriesApi,
    ProductGroupsApi,
    ProductsApi,
)
from centix_api_client_v2.api import <PERSON><PERSON><PERSON>s<PERSON><PERSON> as BrandTypesApiV2

from src.data.mappers.centix_product_mapper import ProductMapper
from src.data.models.centix.product_properties import ProductProperties
from src.domain.models.product import Product
from src.domain.repositories.target_product_repository import TargetProductRepository
from src.exceptions import CentixLoadException, TooManyObjectsException
from src.shared.env import Env
from src.shared.utils.api_client_wrapper import ApiClientWrapper


class CentixProductRepositoryImpl(TargetProductRepository):
    def __init__(
        self,
        env: Env,
        centix_api_client: ApiClientWrapper,
        centix_api_client_v2: ApiClientWrapper,
        product_mapper: ProductMapper,
        document_datasource,
    ) -> None:
        self.env = env
        self._api_client = centix_api_client
        self._product_client = ProductsApi(centix_api_client)
        self._miplan_client = MIPlansApi(centix_api_client)
        self._brand_types_client = BrandTypesApi(centix_api_client)
        self._brand_types_client_v2 = BrandTypesApiV2(centix_api_client_v2)
        self._product_mapper = product_mapper
        self._document_datasource = document_datasource

    def get_product_by_article_number(self, article_number: str) -> CentixAPIDTOReadSalesProduct | None:
        filter_query = f"ID eq '{article_number}'"

        response = self._product_client.sales_products_o_data_list(filter=filter_query)
        if response.total_count is not None and response.total_count == 1 and response.items:
            return response.items[0]
        elif response.total_count is not None and response.total_count > 1 and response.items:
            raise TooManyObjectsException(f"Multiple products found for article number {article_number}")
        return None

    def patch_product_properties(self, auto_id: int, properties: ProductProperties):
        payload = properties.model_dump(by_alias=True, exclude_none=True)
        self._product_client.sales_products_patch(autoid=auto_id, update_properties=payload)

    @cached(cache={})
    def get_brand_by_id(self, brand_name: str) -> CentixAPIDTOReadBrand:
        """Brands are managed in Centix, the requested brand should be expected to exist"""
        brands = BrandsApi(self._api_client).brands_get_list()
        for brand in brands:
            if brand.id == brand_name:
                return brand
        raise CentixLoadException(f"No brand found with name {brand_name}")

    @cached(cache={})
    def get_object_type_by_id(self, type_name: str) -> CentixAPIDTOReadObjectType:
        """Object types are managed in Centix, the requested type should be expected to exist"""
        object_types = ObjectTypesApi(self._api_client).object_types_get_list()
        for object_type in object_types:
            if object_type.id == type_name:
                return object_type
        raise CentixLoadException(f"No ObjectType found with id {type_name}")

    @cached(cache={})
    def get_miplan_by_id(self, plan_id: str) -> CentixAPIDTOReadMI | None:
        """MI plans are managed in Centix, the requested type should be expected to exist"""
        miplans = self._miplan_client.m_i_plans_get_list()
        for miplan in miplans:
            if miplan.id == plan_id:
                return CentixAPIDTOReadMI.model_validate(miplan.model_dump())
        return None

    @cached(cache={})
    def get_product_group_by_id(self, group_id: str) -> CentixAPIDTOReadProductGroup:
        """Product groups are managed in Centix, the requested type should be expected to exist"""
        product_groups = ProductGroupsApi(self._api_client).product_groups_get_list()
        for product_group in product_groups:
            if product_group.id == group_id:
                return product_group
        raise CentixLoadException(f"No ProductGroup found with id {group_id}")

    @cached(cache={})
    def get_product_category_by_id(self, category_id: str) -> CentixAPIDTOReadProductCategory:
        """Product Categories are managed in Centix, the requested type should be expected to exist"""
        product_categories = ProductCategoriesApi(self._api_client).product_categories_get_list()
        for product_category in product_categories:
            if product_category.id == category_id:
                return product_category
        raise CentixLoadException(f"No ProductCategory found with id {category_id}")

    def get_brand_type_by_id(self, brand_type_id: str) -> CentixAPIDTOReadBrandType | None:
        filter_query = f"ID eq '{brand_type_id}'"

        response = self._brand_types_client_v2.brand_types_o_data_list(filter=filter_query)

        items = response.items
        if len(items) > 1:
            logging.warning(f"Found {len(items)} BrandTypes, removing archived records.")
            items = [item for item in response.items if not item.archive]

        if items is not None and len(items) == 1:
            return CentixAPIDTOReadBrandType.model_validate(items[0].model_dump())
        elif items is not None and len(items) > 1 and items:
            logging.error(f"Multiple brand types found for id {brand_type_id}, selecting the first one.")
            return CentixAPIDTOReadBrandType.model_validate(items[0].model_dump())
        return None

    def get_default_mi_plan_for_brand_type(
        self, brand_type_id: int
    ) -> CentixAPIDTOReadBrandTypeDefaultMISchedule | None:
        schedules = self._brand_types_client.brand_types_default_mi_schedules(brand_type_id)
        match len(schedules):
            case 1:
                return schedules[0]
            case c if c > 1:
                raise TooManyObjectsException(f"Multiple default MI plans found for id {brand_type_id}")
            case _:
                return None

    def delete_default_mi_plan(self, default_mi_plan_id: int) -> None:
        BrandTypeDefaultMISchedulesApi(self._api_client).brand_type_default_mi_schedules_delete(default_mi_plan_id)

    def create_default_mi_plan(
        self, default_plan: CentixAPIDTOCreateBrandTypeDefaultMISchedule
    ) -> CentixAPIDTOReadBrandTypeDefaultMISchedule:
        result = BrandTypeDefaultMISchedulesApi(self._api_client).brand_type_default_mi_schedules_create(default_plan)
        return CentixAPIDTOReadBrandTypeDefaultMISchedule.model_validate(result.model_dump())

    def create_brand_type(self, brand_type: CentixAPIDTOCreateBrandType) -> CentixAPIDTOReadBrandType:
        return self._brand_types_client.brand_types_create(brand_type)

    def update_brand_type(self, auto_id: int, brand_type: CentixAPIDTOUpdateBrandType) -> CentixAPIDTOReadBrandType:
        return self._brand_types_client.brand_types_update(auto_id, brand_type)

    def delete_brand_type(self, auto_id: int) -> None:
        return self._brand_types_client.brand_types_delete(auto_id)

    def create_product(self, centix_product: CentixAPIDTOReadSalesProduct) -> CentixAPIDTOReadSalesProduct:
        return self._product_client.sales_products_create(centix_product)

    def update_product(
        self, auto_id: int, centix_product: CentixAPIDTOUpdateSalesProduct
    ) -> CentixAPIDTOReadSalesProduct:
        return self._product_client.sales_products_update(auto_id, centix_product)

    def delete_product(self, auto_id: int) -> None:
        self._product_client.sales_products_delete(auto_id)

    def patch_product_translations(self, auto_id: int, translations: list[CentixAPIDTOContentLanguageItem]) -> None:
        self._product_client.sales_products_patch_translated_properties(auto_id, translations)

    def update_image_for_product(self, auto_id: int, img_path: str) -> None:
        self._product_client.sales_products_set_images(auto_id, img_path)

    def delete_image_for_product(self, auto_id: int) -> None:
        self._product_client.sales_products_remove_images(auto_id)

    def _verify_and_upsert_brand_type_and_mi_plan(
        self,
        brand: CentixAPIDTOReadBrand,
        object_type: CentixAPIDTOReadObjectType,
        centix_product: CentixAPIDTOReadSalesProduct,
        product: Product,
        properties: ProductProperties,
    ) -> None:
        """Verify and upsert brand type and MI plan."""
        new_brand_type = self._product_mapper.to_centix_brand_type(brand, object_type, centix_product, product)
        brand_type = self._upsert_brand_type(new_brand_type)
        logging.debug(f"Updated brand type {brand_type.auto_id}")

        if mi_plan := self.get_miplan_by_id(str(properties.hip_classification)):
            logging.debug(f"Collected MI plan {mi_plan.auto_id}")
            self._upsert_default_mi_plan(brand_type, mi_plan)
        else:
            logging.warning(f"No MI plan found with id {properties.hip_classification}")

    def _upsert_brand_type(self, brand_type: CentixAPIDTOCreateBrandType) -> CentixAPIDTOReadBrandType:
        """Upsert a brand type in Centix."""
        logging.info(f"Collecting brand type for {brand_type.id}")
        existing_brand_type = self.get_brand_type_by_id(brand_type.id)

        if existing_brand_type is None:
            logging.info(f"Creating new brand type for {brand_type.id}")
            return self.create_brand_type(brand_type)
        else:
            # For now we will always update the existing brand type.
            logging.info(f"Updating brand type {existing_brand_type.auto_id}")
            update_brand_type = CentixAPIDTOUpdateBrandType(  # type: ignore
                default_object_type_id=brand_type.default_object_type_id,
                default_product_id=brand_type.default_product_id,
                id=brand_type.id,
                archive=brand_type.archive,
                descr=brand_type.descr,
            )
            return self.update_brand_type(existing_brand_type.auto_id, update_brand_type)

    def _upsert_default_mi_plan(self, brand_type: CentixAPIDTOReadBrandType, mi_plan: CentixAPIDTOReadMI) -> None:
        """Upsert a default MI plan for a brand type."""
        default_mi_plan = self.get_default_mi_plan_for_brand_type(brand_type.auto_id)

        if not default_mi_plan:
            logging.debug("Creating default mi plan")
            default_plan = self._product_mapper.to_brand_type_default_mi_plan(brand_type.auto_id, mi_plan.auto_id)
            self.create_default_mi_plan(default_plan)
        elif mi_plan.auto_id != default_mi_plan.mi_plan_id:
            logging.debug(f"Replacing existing default mi plan {default_mi_plan.mi_plan_id} for {mi_plan.auto_id}")
            self.delete_default_mi_plan(default_mi_plan.auto_id)
            default_plan = self._product_mapper.to_brand_type_default_mi_plan(brand_type.auto_id, mi_plan.auto_id)
            self.create_default_mi_plan(default_plan)
        else:
            logging.debug("No need to update default MI plan for brand type")

    def upsert(
        self,
        product: Product,
        assets: list,
        img_path: str | None = None,
    ) -> None:
        """Upsert a product in Centix with its associated asset and documents."""
        centix_product = self.get_product_by_article_number(product.article_number)
        product_auto_id = centix_product.auto_id if centix_product else None
        new_product = centix_product is None

        brand = self.get_brand_by_id(product.brand.brand_name)
        object_type = self.get_object_type_by_id(self.env.centix_object_type_id)

        if new_product:
            centix_product = self._product_mapper.to_centix_read_sales_product(
                product, brand, object_type, centix_product
            )
            centix_product = self.create_product(centix_product)
        else:
            update_dto = self._product_mapper.to_centix_update_sales_product(
                product, brand, object_type, centix_product
            )
            centix_product = self.update_product(product_auto_id, update_dto)

        properties = self._product_mapper.to_centix_sales_product_properties(product)
        self.patch_product_properties(centix_product.auto_id, properties)

        translations = self._product_mapper.to_centix_sales_product_translations(product)
        self.patch_product_translations(centix_product.auto_id, translations)

        # Verify and upsert brand type and MI plan
        self._verify_and_upsert_brand_type_and_mi_plan(brand, object_type, centix_product, product, properties)

        # Handle product image if img_path is provided
        if img_path:
            self.update_image_for_product(centix_product.auto_id, img_path)

        # Upsert documents using the document datasource
        if assets is not None:
            self._document_datasource.upsert(centix_product.auto_id, product, assets)
