from datetime import datetime
from typing import Any
from typing import Dict
from typing import List
from typing import Optional

import requests
from pydantic import SecretStr
from requests import Response

from src.data.mappers.sync_force_asset_mapper import SyncForceAssetMapper
from src.data.mappers.sync_force_directory_file_mapper import SyncForceDirectoryFileMapper
from src.data.mappers.sync_force_product_mapper import SyncF<PERSON>ceProductMapper
from src.data.mappers.sync_force_proposition_mapper import SyncForcePropositionMapper
from src.data.mappers.sync_force_proposition_response_mapper import SyncForcePropositionResponseMapper
from src.data.models.sync_force.sync_force_directory_file import SyncForceDirectoryFile
from src.data.models.sync_force.syncforce_asset_schema import AssetDetails as SyncForceAssetDetails
from src.data.models.sync_force.syncforce_product_schema import Product as SyncForceProduct
from src.domain.models.asset import AssetDetails
from src.domain.models.directory_file import DirectoryFile
from src.domain.models.product import Product
from src.domain.models.proposition import Proposition
from src.domain.repositories.datahub_product_repository import SourceProductRepository
from src.exceptions import UnAuthorizedException

# MIME types to ignore


class DatahubProductRepositoryImpl(SourceProductRepository):
    def __init__(self, base_url: str, api_key: SecretStr) -> None:
        self._base_url = base_url
        self._api_key = api_key

    def __fetch_data(self, url: str, params: Optional[Dict[str, Any]] = None) -> Response:
        headers = {
            "accept": "application/json",
            "Authorization": f"Bearer {self._api_key.get_secret_value()}",
            "User-Agent": "Linux-holmatro-centix",
        }
        response = requests.get(url=url, params=params, headers=headers, timeout=15)
        if response.status_code == 401:
            raise UnAuthorizedException(message=response.text)
        response.raise_for_status()
        return response

    def get_directory_files(self, sub_directory: str, modified_since: Optional[datetime] = None) -> List[DirectoryFile]:
        """Fetches a list of files from a directory (root by default)."""
        url = self._base_url + "/api/products/files/list_objects"
        params = {"blob_path": sub_directory}
        if modified_since is not None:
            # Add parameter if not empty
            params["modified_since"] = modified_since.strftime("%Y-%m-%d")

        response = self.__fetch_data(url, params)
        response_json = response.json()

        # Convert the directory data to SyncForceDirectoryFile objects
        sync_force_directory_files = [SyncForceDirectoryFile(**directory) for directory in response_json]

        # Use the mapper to convert the SyncForceDirectoryFile objects to domain DirectoryFile objects
        return [
            SyncForceDirectoryFileMapper.to_domain_directory_file(directory_file)
            for directory_file in sync_force_directory_files
        ]

    def get_file_contents(self, file_name: str) -> Any:
        """method to fetch datahub file contents."""
        url = self._base_url + "/api/products/files"
        params = {"file_name": file_name}
        response = self.__fetch_data(url, params)
        return response.json() if file_name.endswith(".json") else response.content

    def get_last_modified_header(self, file_path: str) -> str:
        url = self._base_url + "/api/products/files"
        params = {"file_name": file_path}
        response = self.__fetch_data(url, params)
        return response.headers["Last-Modified"]

    def get_product_by_id(self, product_id: str) -> Product:
        """
        Fetches a product by its ID and returns a domain model Product.

        Args:
            product_id: The ID of the product to fetch

        Returns:
            A domain model Product
        """
        product_data = self.get_file_contents(f"product-details-{product_id}.json")["Product"]

        # Convert the product data to a SyncForceProduct
        sync_force_product = SyncForceProduct(**product_data)

        # Use the mapper to convert the SyncForceProduct to a domain Product
        return SyncForceProductMapper.to_domain_product(sync_force_product)

    def get_proposition_by_id(self, proposition_id: str) -> Proposition:
        """
        Fetches a proposition by its ID and returns a domain model Proposition.

        Args:
            proposition_id: The ID of the proposition to fetch

        Returns:
            A domain model Proposition
        """
        proposition_data = self.get_file_contents(f"proposition-{proposition_id}.json")

        # Convert the proposition data to a SyncForceProposition using the custom mapper
        sync_force_proposition = SyncForcePropositionResponseMapper.to_model(proposition_data)

        # Use the mapper to convert the SyncForceProposition to a domain Proposition
        return SyncForcePropositionMapper.to_domain_proposition(sync_force_proposition)

    def get_asset_by_id(self, asset_id: str) -> AssetDetails:
        """
        Fetches an asset by its ID and returns a domain model AssetDetails.

        Args:
            asset_id: The ID of the asset to fetch

        Returns:
            A domain model AssetDetails
        """
        asset_data = self.get_file_contents(f"assets/{asset_id}/asset-details.json")

        directory_files = self.get_directory_files(f"assets/{asset_id}")
        # Convert the asset data to a SyncForceAssetDetails
        sync_force_asset = SyncForceAssetDetails.model_validate(asset_data)
        # Use the mapper to convert the SyncForceAssetDetails to a domain AssetDetails
        return SyncForceAssetMapper.to_domain_asset(sync_force_asset, directory_files=directory_files)
