from src.data.models.sync_force.syncforce_proposition_schema import Category as SyncForceCategory
from src.data.models.sync_force.syncforce_proposition_schema import (
    Content,
    DigitalAssetRole,
    Language,
    Market,
    TextRole,
    Title,
    Translation,
)
from src.data.models.sync_force.syncforce_proposition_schema import ProductItem as SyncForceProductItem
from src.data.models.sync_force.syncforce_proposition_schema import Proposition as SyncForceProposition


class SyncForcePropositionResponseMapper:
    @staticmethod
    def to_model(data: dict) -> SyncForceProposition:
        return SyncForceProposition(
            guid=data["GUID"],
            reference=data["reference"],
            title=SyncForcePropositionResponseMapper._map_title(data["title"]),
            markets=[SyncForcePropositionResponseMapper._map_market(m) for m in data.get("markets", [])],
            text_roles=[SyncForcePropositionResponseMapper._map_text_role(tr) for tr in data.get("text_roles", [])],
            digital_asset_roles=[
                SyncForcePropositionResponseMapper._map_digital_asset_role(dr)
                for dr in data.get("digital_asset_roles", [])
            ],
            categories=[SyncForcePropositionResponseMapper._map_category(c) for c in data.get("categories", [])],
        )

    @staticmethod
    def _map_translation(data: dict) -> Translation:
        return Translation(
            language=data["language"],
            label=data["label"],
        )

    @staticmethod
    def _map_product_item(data: dict) -> SyncForceProductItem:
        return SyncForceProductItem(
            umid=data["umid"],
            pmid=data["pmid"],
            change_date=data["changeDate"],
            digital_life_cycle_status=data["digitalLifeCycleStatus"],
            sort_index=data["sortIndex"],
            star=data["star"],
        )

    @staticmethod
    def _map_content(data: dict) -> Content:
        return Content(
            id=data["id"],
            role_id=data["roleId"],
            translations=[SyncForcePropositionResponseMapper._map_translation(t) for t in data.get("translations", [])],
        )

    @staticmethod
    def _map_category(data: dict) -> SyncForceCategory:
        return SyncForceCategory(
            guid=data["GUID"],
            id=data["id"],
            sort_index=data["sortIndex"],
            translations=[SyncForcePropositionResponseMapper._map_translation(t) for t in data.get("translations", [])],
            products=(
                [SyncForcePropositionResponseMapper._map_product_item(p) for p in data.get("products", [])]
                if data.get("products")
                else None
            ),
            contents=(
                [SyncForcePropositionResponseMapper._map_content(c) for c in data.get("contents", [])]
                if data.get("contents")
                else None
            ),
            digital_assets=data.get("digital_assets"),
            related_categories=data.get("related_categories"),
            categories=(
                [SyncForcePropositionResponseMapper._map_category(c) for c in data.get("categories", [])]
                if data.get("categories")
                else None
            ),
        )

    @staticmethod
    def _map_title(data: dict) -> Title:
        return Title(
            translations=[SyncForcePropositionResponseMapper._map_translation(t) for t in data.get("translations", [])]
        )

    @staticmethod
    def _map_language(data: dict) -> Language:
        return Language(language=data["language"])

    @staticmethod
    def _map_market(data: dict) -> Market:
        return Market(
            id=data["id"],
            name=data["name"],
            reference=data.get("reference"),
            languages=[
                SyncForcePropositionResponseMapper._map_language(language) for language in data.get("languages", [])
            ],
        )

    @staticmethod
    def _map_text_role(data: dict) -> TextRole:
        return TextRole(
            id=data["id"],
            sort_index=data["sort_index"],
            bref=data.get("BREF"),
            translations=[SyncForcePropositionResponseMapper._map_translation(t) for t in data.get("translations", [])],
        )

    @staticmethod
    def _map_digital_asset_role(data: dict) -> DigitalAssetRole:
        return DigitalAssetRole(
            id=data["id"],
            sort_index=data["sort_index"],
            bref=data.get("BREF"),
            translations=[SyncForcePropositionResponseMapper._map_translation(t) for t in data.get("translations", [])],
        )
