from centix_api_client import CentixAPIDTOReadDocumentKind

from src.domain.models.document_kind import DocumentKind


class DocumentKindMapper:
    """
    Mapper to convert from CentixAPIDTOReadDocumentKind to DocumentKind model.
    """

    @staticmethod
    def to_domain(centix_document_kind: CentixAPIDTOReadDocumentKind) -> DocumentKind:
        """
        Convert a CentixAPIDTOReadDocumentKind to a DocumentKind domain model.

        Args:
            centix_document_kind: The source CentixAPIDTOReadDocumentKind instance.

        Returns:
            DocumentKind: A new DocumentKind domain model instance.
        """
        return DocumentKind(
            id=centix_document_kind.id,
            auto_id=centix_document_kind.auto_id,
        )
