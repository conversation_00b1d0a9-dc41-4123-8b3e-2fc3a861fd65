from src.data.models.sync_force.syncforce_product_schema import Product as SyncForceProduct
from src.domain.models.product import Product


class SyncForceProductMapper:
    """Mapper for converting SyncForceProduct to domain Product model."""

    @staticmethod
    def to_domain_product(sync_force_product: SyncForceProduct) -> Product:
        """
        Convert a SyncForceProduct to a domain Product model.

        Args:
            sync_force_product: The SyncForceProduct to convert

        Returns:
            A domain Product model
        """
        product_dict = sync_force_product.model_dump()
        return Product(**product_dict)
