from src.data.models.sync_force.syncforce_asset_schema import AssetDetails as SyncForceAssetDetails
from src.domain.models.asset import AssetDetails


class SyncForceAssetMapper:
    """Mapper for converting SyncForceAssetDetails to domain AssetDetails model."""

    @staticmethod
    def to_domain_asset(sync_force_asset: SyncForceAssetDetails, directory_files=None) -> AssetDetails:
        """
        Convert a SyncForceAssetDetails to a domain AssetDetails model.

        Args:
            sync_force_asset: The SyncForceAssetDetails to convert
            directory_files: List of SyncForceDirectoryFile for the asset's directory (to get file sizes)

        Returns:
            A domain AssetDetails model
        """
        asset_dict = sync_force_asset.model_dump()
        if directory_files is not None:
            file_size_map = {f.key.split("/")[-1]: f.size for f in directory_files}
            for file in asset_dict.get("files", []):
                file["size"] = file_size_map.get(file["file_name"], 0)
        return AssetDetails(**asset_dict)
