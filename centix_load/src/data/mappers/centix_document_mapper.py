import logging
import re
from urllib.parse import quote

from centix_api_client import CentixAPIDTOCreateDocumentFromUrl, CentixAPIDTOReadDocument

from src.domain.models.asset import AssetDetails, File
from src.domain.models.document import Document, FileUriType
from src.domain.models.document_kind import DocumentKind
from src.domain.models.product import Product
from src.shared.enum import CentixLanguageIds, LanguageEnum
from src.shared.env import Env

env = Env()
logger = logging.getLogger(__name__)


class DocumentMapper:
    """
    Mapper for Document domain model to/from various DTOs.
    """

    @staticmethod
    def create_document_from_assets(
        file: File,
        asset: AssetDetails,
        product: Product,
        file_size: int | None,
        language: LanguageEnum,
        document_kind: DocumentKind,
    ) -> Document:
        """
        Create a Document domain model from asset information.

        Args:
            file: File information
            asset: Asset details
            product: Product information
            file_size: Size of the file in bytes
            language: Document language
            document_kind: Document kind information

        Returns:
            Document: A domain model Document
        """
        file_name = file.file_name
        description = asset.get_translation(language)
        if description is None:
            description = file_name
        if len(description) > 100:
            logging.warning(f"Description of length {len(description)} too long, reducing to the first 100 characters")
            description = description[:100]

        sanitized_file = DocumentMapper.sanitize_desired_filename(description)
        logger.info(f"Sanitized_file: {sanitized_file} and description {description}")

        name, extension = file.file_name.split(".")
        document_id = f"{product.article_number}/{asset.reference_nr}/{language.name}"

        # Create the document domain model
        document = Document(
            id=document_id,
            descr=description,
            document_kind_id=document_kind.auto_id,
            language_id=CentixLanguageIds[language.name].value,
            file_name=name,
            file_extension=f".{extension}",
            file_size=file_size,
            asset_udai=asset.udai,
            sanitized_file_name=sanitized_file,
            is_public_media=document_kind.id == "PM",
        )
        return document

    @staticmethod
    def to_centix_create_document_from_url(document: Document) -> CentixAPIDTOCreateDocumentFromUrl:
        """
        Convert a Document domain model to CentixAPIDTOCreateDocumentFromUrl.

        Args:
            document: The domain Document model

        Returns:
            CentixAPIDTOCreateDocumentFromUrl: The Centix DTO for document creation
        """
        # Generate the URL based on document properties
        extension = document.file_extension.lstrip(".") if document.file_extension else ""
        if document.sanitized_file_name is None:
            if document.descr is None:
                raise ValueError("Document description is required to generate the URL.")
            sanitized_file = DocumentMapper.sanitize_desired_filename(document.descr)
        else:
            sanitized_file = quote(document.sanitized_file_name)

        if getattr(document, "is_public_media", False):
            # Public Media
            url = (
                f"{env.datahub_api_base_url}/api/products/public/assets?asset_id={document.asset_udai}"
                f"&file_name={document.file_name}{document.file_extension}&desired_filename={sanitized_file}.{extension}"
            )
        else:
            # Dealer Media
            url = (
                f"{env.datahub_api_base_url}/api/products/assets?asset_id={document.asset_udai}"
                f"&file_name={document.file_name}{document.file_extension}&desired_filename={sanitized_file}.{extension}"
            )

        # Create the Centix DTO
        centix_document = CentixAPIDTOCreateDocumentFromUrl(  # type: ignore
            id=document.id,
            descr=document.descr,
            document_kind_id=document.document_kind_id,
            language_id=document.language_id,
            url=url,
            file_name=document.file_name,
            file_extension=document.file_extension,
            file_size=document.file_size,
            reuse_document=False,
        )
        return centix_document

    @staticmethod
    def sanitize_desired_filename(description: str) -> str:
        """Sanitize a filename by removing invalid characters."""
        invalid_chars = r'[\\/:*?"<>;|,]'
        sanitized = re.sub(invalid_chars, "", description)
        sanitized = sanitized.strip().strip(".")
        return sanitized

    @staticmethod
    def to_domain(centix_document: CentixAPIDTOReadDocument) -> Document:
        """
        Convert a CentixAPIDTOReadDocument to a Document domain model.

        Args:
            centix_document: The source CentixAPIDTOReadDocument instance.

        Returns:
            Document: A new Document domain model instance.
        """
        file_uri_type_value = None
        if centix_document.file_uri_type is not None:
            try:
                file_uri_type_value = FileUriType(centix_document.file_uri_type)
            except ValueError:
                raise ValueError(f"Invalid file URI type: {centix_document.file_uri_type}")

        return Document(
            id=centix_document.id,
            auto_id=centix_document.auto_id,
            language_id=centix_document.language_id,
            descr=centix_document.descr,
            document_kind_id=centix_document.document_kind_id,
            file_uri_type=file_uri_type_value,
            file_name=centix_document.file_name,
            file_extension=centix_document.file_extension,
            file_size=centix_document.file_size,
            time_stamp=centix_document.time_stamp,
        )
