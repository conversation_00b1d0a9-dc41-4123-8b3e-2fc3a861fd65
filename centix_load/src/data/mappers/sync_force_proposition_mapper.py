from src.data.models.sync_force.syncforce_proposition_schema import Category as SyncForceCategory
from src.data.models.sync_force.syncforce_proposition_schema import ProductItem as SyncForceProductItem
from src.data.models.sync_force.syncforce_proposition_schema import Proposition as SyncForceProposition
from src.data.models.sync_force.syncforce_proposition_schema import Translation as SyncForceTranslation
from src.domain.models.proposition import Category, CategoryTranslation, ProductItem, Proposition
from src.shared.enum import LanguageEnum


class SyncForcePropositionMapper:
    """Mapper for converting SyncForceProposition to domain Proposition model."""

    @staticmethod
    def to_domain_proposition(sync_force_proposition: SyncForceProposition) -> Proposition:
        return Proposition(
            guid=sync_force_proposition.guid,
            categories=[
                SyncForcePropositionMapper._map_category(category) for category in sync_force_proposition.categories
            ],
        )

    @staticmethod
    def _map_category(syncforce_category: SyncForceCategory) -> Category:
        return Category(
            guid=syncforce_category.guid,
            id=syncforce_category.id,
            sort_index=syncforce_category.sort_index,
            translations=[SyncForcePropositionMapper._map_translation(t) for t in syncforce_category.translations],
            products=[SyncForcePropositionMapper._map_product(p) for p in syncforce_category.products or []],
            sub_categories=[SyncForcePropositionMapper._map_category(c) for c in syncforce_category.categories or []],
        )

    @staticmethod
    def _map_translation(translation: SyncForceTranslation) -> CategoryTranslation:
        return CategoryTranslation(language=LanguageEnum(translation.language), label=translation.label)

    @staticmethod
    def _map_product(product: SyncForceProductItem) -> ProductItem:
        return ProductItem(
            umid=product.umid,
            pmid=product.pmid,
            change_date=product.change_date,
            digital_life_cycle_status=product.digital_life_cycle_status,
            sort_index=product.sort_index,
            star=product.star,
        )
