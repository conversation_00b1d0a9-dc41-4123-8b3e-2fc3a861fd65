import logging
from typing import Dict
from typing import List
from typing import Union

from centix_api_client import CentixAPIDTOContentLanguageItem
from centix_api_client import CentixAPIDTOCreateBrandType
from centix_api_client import CentixAPID<PERSON>CreateBrandTypeDefaultMISchedule
from centix_api_client import Centix<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>rand
from centix_api_client import Centix<PERSON><PERSON>TOReadObjectType
from centix_api_client import CentixAPIDTOReadSalesProduct
from centix_api_client import CentixAPIDTOUpdateSalesProduct

from src.data.models.centix.product_properties import ProductProperties
from src.domain.models.product import Product
from src.shared.enum import AttributeIds
from src.shared.enum import LanguageEnum
from src.shared.enum import TextIds
from src.shared.field_sanitizer import sanitize_length

logger = logging.getLogger(__name__)


class ProductMapper:
    FIXED_PRODUCT_ATTRIBUTES = {
        "currency_id": 1,
        "purchase_currency_id": 1,
        "purchase_vat_id": 1,
        "stock_type": 1,
        "vat_id": 2,
        "status_id": 1,
    }

    @staticmethod
    def to_centix_read_sales_product(
        product: Product,
        brand: CentixAPIDTOReadBrand,
        object_type: CentixAPIDTOReadObjectType,
        centix_product: CentixAPIDTOReadSalesProduct | None = None,
    ) -> CentixAPIDTOReadSalesProduct:
        """
        Creating a Centix Sales Product DTO to be used for creating or updateng a sales product.
        The 'Read' DTO is used for all operations."""
        centix_product = centix_product or CentixAPIDTOReadSalesProduct()

        centix_product.id = product.article_number
        centix_product.descr = sanitize_length(product.name.get_translation(LanguageEnum.EN), 100)
        centix_product.brand_id = brand.auto_id
        centix_product.object_type_id = object_type.auto_id

        for attr, value in ProductMapper.FIXED_PRODUCT_ATTRIBUTES.items():
            setattr(centix_product, attr, value)
        centix_product.descr2 = ProductMapper.get_centix_product_descr2(product)

        return centix_product

    @staticmethod
    def to_centix_update_sales_product(
        product: Product,
        brand: CentixAPIDTOReadBrand,
        object_type: CentixAPIDTOReadObjectType,
        centix_product: CentixAPIDTOReadSalesProduct | None = None,
    ) -> CentixAPIDTOUpdateSalesProduct:
        """
        Creating a Centix Sales Product DTO to be used for creating or updating a sales product.
        The 'Update' DTO is used for all update operations."""
        if centix_product is not None:
            update_product = CentixAPIDTOUpdateSalesProduct(  # type: ignore
                id=centix_product.id,
                descr=centix_product.descr,
                brand_id=centix_product.brand_id,
                object_type_id=centix_product.object_type_id,
                currency_id=centix_product.currency_id,
                purchase_currency_id=centix_product.purchase_currency_id,
                purchase_vat_id=centix_product.purchase_vat_id,
                stock_type=centix_product.stock_type,
                vat_id=centix_product.vat_id,
                status_id=centix_product.status_id,
                descr2=centix_product.descr2,
                price=getattr(centix_product, "price", None),
                product_category_id=getattr(centix_product, "product_category_id", None),
                product_group_id=getattr(centix_product, "product_group_id", None),
            )
        else:
            update_product = CentixAPIDTOUpdateSalesProduct()  # type: ignore

        update_product.id = product.article_number
        update_product.descr = sanitize_length(product.name.get_translation(LanguageEnum.EN), 100)
        update_product.brand_id = brand.auto_id
        update_product.object_type_id = object_type.auto_id

        for attr, value in ProductMapper.FIXED_PRODUCT_ATTRIBUTES.items():
            setattr(update_product, attr, value)
        update_product.descr2 = ProductMapper.get_centix_product_descr2(product)

        return update_product

    @staticmethod
    def get_centix_product_descr2(product: Product) -> str | None:
        """Get the product description in the specified language."""
        for text in product.texts:
            if text.id == TextIds.LONG_DESCRIPTION.value:
                return text.get_translation(LanguageEnum.EN)
        return None

    @staticmethod
    def to_centix_sales_product_properties(product: Product) -> ProductProperties:  # noqa: CCR001
        properties: Dict[str, Union[float, int, str, bool, None]] = {}

        for attr in product.attributes.attribute:
            match attr.id:
                case AttributeIds.MAX_WORKING_PRESSURE_BAR.value:
                    properties["MaxWorkingPressureBar"] = float(attr.values[0].content) if attr.values else None
                case AttributeIds.MAX_WORKING_PRESSURE_PSI.value:
                    properties["MaxWorkingPressurePSI"] = float(attr.values[0].content) if attr.values else None
                case AttributeIds.HIP_CLASSIFICATION.value:
                    properties["HIPClassification"] = int(attr.values[0].content) if attr.values else None
                case AttributeIds.MODEL.value:
                    properties["Model"] = attr.values[0].content if attr.values else None
                case AttributeIds.SERVICE_BRAND.value:
                    properties["ServiceBrand"] = attr.values[0].content if attr.values else None
                case AttributeIds.BATTERY_INCLUDED.value:
                    properties["BatteryIncluded"] = (
                        attr.values[0].get_translation(LanguageEnum.EN) == "yes" if attr.values else None
                    )
        return ProductProperties.model_validate(properties)

    @staticmethod
    def to_centix_sales_product_translations(product: Product) -> List[CentixAPIDTOContentLanguageItem]:
        """Generates product translations for Centix."""
        translations = []
        for language in (LanguageEnum.NL, LanguageEnum.DE):
            translations.extend(ProductMapper._get_translations_for_language(product, language))
        return translations

    @staticmethod
    def _get_translations_for_language(
        product: Product, language: LanguageEnum
    ) -> List[CentixAPIDTOContentLanguageItem]:
        """Retrieve translations for a specific language."""
        translation_items = [
            ProductMapper._create_translation_item(language, "Descr", product.name.get_translation(language) or "")
        ]

        descr2 = ProductMapper._get_long_description(product, language)
        if descr2:
            translation_items.append(ProductMapper._create_translation_item(language, "Descr2", descr2))

        return translation_items

    @staticmethod
    def _create_translation_item(
        language: LanguageEnum, property_name: str, value: str
    ) -> CentixAPIDTOContentLanguageItem:
        """Create a translation item."""
        return CentixAPIDTOContentLanguageItem(  # type: ignore
            content_language=language.value,
            var_property=sanitize_length(property_name, 100) or "",
            value=value,
        )

    @staticmethod
    def _get_long_description(product: Product, language: LanguageEnum) -> str | None:
        """Retrieve the long description for a given product and language."""
        for text in product.texts:
            if text.id == TextIds.LONG_DESCRIPTION.value:
                return text.get_translation(language)
        return None

    @staticmethod
    def to_centix_brand_type(
        brand: CentixAPIDTOReadBrand,
        object_type: CentixAPIDTOReadObjectType,
        centix_product: CentixAPIDTOReadSalesProduct,
        product: Product,
    ) -> CentixAPIDTOCreateBrandType:
        return CentixAPIDTOCreateBrandType(  # type: ignore
            brand_id=brand.auto_id,
            default_object_type_id=object_type.auto_id,
            default_product_id=centix_product.auto_id,
            archive=False,
            id=product.article_number,
            descr=sanitize_length(product.name.get_translation(LanguageEnum.EN), 100),
        )

    @staticmethod
    def to_brand_type_default_mi_plan(
        brand_type_id: int, mi_plan_id: int
    ) -> CentixAPIDTOCreateBrandTypeDefaultMISchedule:
        """The other values are requied to be set to default values."""
        return CentixAPIDTOCreateBrandTypeDefaultMISchedule(  # type: ignore
            brand_type_id=brand_type_id,
            mi_plan_id=mi_plan_id,
            time_schedule_id=1,
            mi_validation_expires=True,
            mi_valid_until_date_calculation_method=1,
            next_run_calculation_method=0,
        )
