from src.data.models.sync_force.sync_force_directory_file import SyncForceDirectoryFile
from src.domain.models.directory_file import DirectoryFile


class SyncForceDirectoryFileMapper:
    """Mapper for converting SyncForceDirectoryFile to domain DirectoryFile model."""

    @staticmethod
    def to_domain_directory_file(sync_force_directory_file: SyncForceDirectoryFile) -> DirectoryFile:
        """
        Convert a SyncForceDirectoryFile to a domain DirectoryFile model.

        Args:
            sync_force_directory_file: The SyncForceDirectoryFile to convert

        Returns:
            A domain DirectoryFile model
        """
        directory_file_dict = sync_force_directory_file.model_dump()
        return DirectoryFile(**directory_file_dict)
