from woocommerce import API

from src.data.models.woocommerce.product import Product as WCProduct


class ProductNotFound(Exception):
    pass


class WoocommerceProductDatasource:
    def __init__(self, woocommerce_api: API):
        self._woocommerce_api = woocommerce_api

    def get_product_by_sku(self, sku: str) -> WCProduct:
        response = self._woocommerce_api.get("products", params={"sku": sku})
        response.raise_for_status()

        products = response.json()
        if products:
            return self._map_response_product(products[0])
        raise ProductNotFound(f"Product with SKU {sku} not found")

    def create(self, payload: dict) -> WCProduct:
        response = self._woocommerce_api.post("products", payload)
        response.raise_for_status()
        data = response.json()
        return self._map_response_product(data)

    def update(self, product_id: int, payload: dict) -> WCProduct:
        response = self._woocommerce_api.put(f"products/{product_id}", payload)
        response.raise_for_status()
        data = response.json()
        return self._map_response_product(data)

    def delete(self, product_id: int) -> None:
        response = self._woocommerce_api.delete(f"products/{product_id}", params={"force": True})
        response.raise_for_status()

    def _map_response_product(self, product_data: dict) -> WCProduct:
        return WCProduct(
            id=product_data["id"],
            name=product_data["name"],
            sku=product_data["sku"],
            description=product_data.get("description"),
            category_ids=[cat["id"] for cat in product_data.get("categories", [])],
        )
