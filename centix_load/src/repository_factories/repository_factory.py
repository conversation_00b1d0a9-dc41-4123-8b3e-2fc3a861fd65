from abc import ABC, abstractmethod

from src.domain.repositories.datahub_product_repository import SourceProductRepository
from src.domain.repositories.target_category_repository import TargetCategoryRepository
from src.domain.repositories.target_product_repository import TargetProductRepository
from src.shared.env import Env


class RepositoryFactory(ABC):
    """Abstract base class for sync repository factories"""

    def __init__(self, settings: Env, rate_limited: bool):
        self.settings = settings
        self.rate_limited = rate_limited

    @abstractmethod
    def create_target_product_repository(self) -> TargetProductRepository:
        """Create a target product repository."""
        raise NotImplementedError

    @abstractmethod
    def create_target_category_repository(self) -> TargetCategoryRepository | None:
        """Create a target category repository."""
        raise NotImplementedError

    @abstractmethod
    def create_source_repository(self) -> SourceProductRepository:
        """Create a source repository."""
        raise NotImplementedError
