from centix_api_client import <PERSON><PERSON><PERSON><PERSON>
from centix_api_client import Configuration as ApiClientConfiguration
from centix_api_client_v2 import Api<PERSON>lient as ApiClientV2
from centix_api_client_v2 import Configuration as ApiClientV2Configuration

from src.data.centix_document_datasource import CentixDocumentDatasource
from src.data.centix_product_repository_impl import CentixProductRepositoryImpl
from src.data.datahub_sync_force_data_source_repository import DatahubProductRepositoryImpl
from src.data.mappers.centix_product_mapper import ProductMapper
from src.repository_factories.repository_factory import RepositoryFactory
from src.shared.env import Env
from src.shared.utils.api_client_wrapper import ApiClientWrapper
from src.shared.utils.auth import AuthorizationToken

env = Env()


class DataHubToCentixRepositoryFactory(RepositoryFactory):
    """Centix sync repository factory"""

    def create_target_product_repository(self) -> CentixProductRepositoryImpl:
        auth = AuthorizationToken()
        token_refresh_hook = auth.refresh_token
        retry_strategy = env.retry_strategy

        config_v1 = ApiClientConfiguration(host=env.centix_api_base_url, retries=retry_strategy)  # type: ignore
        config_v2 = ApiClientV2Configuration(host=env.centix_api_base_url, retries=retry_strategy)  # type: ignore

        client = ApiClient(configuration=config_v1)
        ca_client = ApiClientWrapper(client, token_refresh_hook)

        client_v2 = ApiClientV2(configuration=config_v2)
        ca_client_v2 = ApiClientWrapper(client_v2, token_refresh_hook)
        product_mapper = ProductMapper()
        centix_document_datasource = CentixDocumentDatasource(ca_client, self.settings)

        return CentixProductRepositoryImpl(
            self.settings, ca_client, ca_client_v2, product_mapper, centix_document_datasource
        )

    def create_target_category_repository(self) -> None:
        return None

    def create_source_repository(self) -> DatahubProductRepositoryImpl:
        return DatahubProductRepositoryImpl(
            base_url=self.settings.datahub_api_base_url,
            api_key=self.settings.datahub_api_key,
        )
