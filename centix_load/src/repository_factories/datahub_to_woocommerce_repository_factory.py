from woocommerce import API

from src.data.datahub_sync_force_data_source_repository import DatahubProductRepositoryImpl
from src.data.woocommerce_category_datasource import WoocommerceCategoryDatasource
from src.data.woocommerce_category_repository_impl import WoocommerceCategoryRepositoryImpl
from src.data.woocommerce_product_datasource import WoocommerceProductDatasource
from src.data.woocommerce_product_repository_impl import WoocommerceProductRepositoryImpl
from src.repository_factories.repository_factory import RepositoryFactory


class DataHubToWoocommerceRepositoryFactory(RepositoryFactory):
    """Woocommerce sync repository factory"""

    def _create_wc_api(self) -> API:
        return API(
            url=self.settings.woocommerce_api_base_url,
            consumer_key=self.settings.woocommerce_api_consumer_key,
            consumer_secret=self.settings.woocommerce_api_consumer_secret,
            version=self.settings.woocommerce_api_version,
        )

    def create_target_product_repository(self) -> WoocommerceProductRepositoryImpl:
        wc_api = self._create_wc_api()
        datasource_category = WoocommerceCategoryDatasource(wc_api)
        datasource_product = WoocommerceProductDatasource(wc_api)
        return WoocommerceProductRepositoryImpl(wc_api, datasource_category, datasource_product)

    def create_target_category_repository(self) -> WoocommerceCategoryRepositoryImpl:
        wc_api = self._create_wc_api()
        datasource = WoocommerceCategoryDatasource(wc_api)
        return WoocommerceCategoryRepositoryImpl(wc_api, datasource)

    def create_source_repository(self) -> DatahubProductRepositoryImpl:
        return DatahubProductRepositoryImpl(
            base_url=self.settings.datahub_api_base_url,
            api_key=self.settings.datahub_api_key,
        )
