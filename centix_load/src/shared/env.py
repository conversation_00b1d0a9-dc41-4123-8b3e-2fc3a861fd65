import os

from pydantic import SecretStr
from pydantic_settings import BaseSettings, SettingsConfigDict
from urllib3 import Retry


class Env(BaseSettings):
    model_config = SettingsConfigDict()

    datahub_api_base_url: str = ""
    datahub_api_key: SecretStr = SecretStr("")
    timedelta_day_increments: int = 1

    stage: str = "LOCAL"
    logging_level: str = "INFO"

    centix_api_base_url: str = ""
    centix_api_token_url: str = ""
    centix_api_client_id: str = ""
    centix_api_client_secret: str = ""

    centix_object_type_id: str = "D"  # standard object type id for products
    centix_force_update_documents: bool = False

    # Woocommerce
    woocommerce_api_base_url: str = ""
    woocommerce_api_consumer_key: str = ""
    woocommerce_api_consumer_secret: str = ""
    woocommerce_api_version: str = "wc/v3"

    sentry_dsn: str = ""

    this_dir: str = os.path.dirname(os.path.realpath(__file__))
    base_dir: str = os.path.dirname(this_dir)
    mounted_files_path: str = os.path.join(base_dir, "mounted_files")
    retry_strategy: Retry = Retry(
        total=2,
        backoff_factor=0.5,
        backoff_jitter=1.0,
        status_forcelist=[429],
        respect_retry_after_header=True,
        allowed_methods=["DELETE", "GET", "HEAD", "OPTIONS", "PUT", "TRACE", "POST", "PATCH"],
    )
