class ApiClientWrapper:
    def __init__(self, api_client, token_refresh_hook):
        self.api_client = api_client
        self.token_refresh_hook = token_refresh_hook

    def _refresh_token(self):
        if callable(self.token_refresh_hook):
            self.api_client.configuration.access_token = self.token_refresh_hook()

    def __getattr__(self, name):
        attr = getattr(self.api_client, name)
        if callable(attr):

            def wrapped(*args, **kwargs):
                self._refresh_token()
                return attr(*args, **kwargs)

            return wrapped
        return attr
