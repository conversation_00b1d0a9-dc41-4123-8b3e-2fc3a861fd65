import logging
import time

import requests
from cachetools import TTLCache
from requests.exceptions import HTTPError
from requests.exceptions import RequestException

from src.shared.env import Env

env = Env()
logger = logging.getLogger(__name__)


class AuthorizationToken:
    def __init__(self) -> None:
        self.client_id: str = env.centix_api_client_id
        self.client_secret: str = env.centix_api_client_secret
        self.token_url: str = env.centix_api_token_url
        self.cache: TTLCache[str, str] = TTLCache(maxsize=1, ttl=300)

    def refresh_token(self) -> str:
        if "access_token" in self.cache:
            return self.cache["access_token"]
        try:
            logger.info("Requesting new token from server")
            response = requests.post(
                self.token_url,
                headers={"Content-Type": "application/x-www-form-urlencoded"},
                data={
                    "grant_type": "client_credentials",
                    "client_id": self.client_id,
                    "client_secret": self.client_secret,
                },
                timeout=5,
            )
            response.raise_for_status()
            token_data = response.json()

            if "access_token" not in token_data or "expires_in" not in token_data:
                logger.error(f"Failed to parse token response: {token_data}")
                raise ValueError("Invalid token response format")

            token: str = token_data["access_token"]
            expires_in: int = token_data["expires_in"]

            expires_in = expires_in - 10
            self.token_expiry = time.time() + expires_in
            self.cache = TTLCache(maxsize=1, ttl=expires_in)
            self.cache["access_token"] = token
            return token

        except HTTPError as e:
            logger.error(f"Failed to refresh token due to HTTP error: {e}")
            raise Exception("Failed to refresh token due to HTTP error")
        except RequestException as e:
            logger.error(f"Failed to refresh token due to request error: {e}")
            raise Exception("Failed to refresh token due to request error")
