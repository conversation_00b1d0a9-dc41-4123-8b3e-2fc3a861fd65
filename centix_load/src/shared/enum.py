from enum import Enum


class LanguageEnum(str, Enum):
    EN = "en"
    DE = "de"
    FR = "fr"
    ES = "es"
    NL = "nl"
    PL = "pl"
    PT = "pt"
    ZH = "zh"
    RU = "ru"


class AttributeIds(int, Enum):
    MAX_WORKING_PRESSURE_BAR = 4
    MAX_WORKING_PRESSURE_PSI = 6
    HIP_CLASSIFICATION = 1171
    MODEL = 2
    SERVICE_BRAND = 1191
    BATTERY_INCLUDED = 1340


class TextIds(int, Enum):
    LONG_DESCRIPTION = 41


class AssetRoleIds(int, Enum):
    """This is not a complete list of AssetRoleIds, only roles that are used in this application"""

    MAIN_IMAGE = 1004
    PUBLIC_MEDIA = 15
    DEALER_MEDIA = 16
    SPEC_SHEET = 2


class AssetSubTypeIds(int, Enum):
    SERVICE_PRESENTATION = 30
    SERVICE_DOCUMENT = 39
    SERVICE_BROCHURE = 50
    SERVICE_VIDEO = 56
    SERVICE_BULLETIN = 65
    TECHNICAL_SPECIFICATION_SHEET = 35
    USER_MANUAL = 41


class TargetGroups(str, Enum):
    ADMIN = "Admin"
    CENTIX_ADMIN = "Centix-Admin"
    INTERNAL_SALESMANAGER = "Internal Salesmanager"
    MARKETING = "Marketing"
    PRODUCT_MANAGER = "Product Manager"
    R_AND_D = "R&D"
    SALES_INTERNAL_OTHER = "Sales + Internal Other"
    SERVICE = "Service"
    CERTIFIED_DEALER_INDUSTRY = "Certified Dealer (Industry)"
    CERTIFIED_DEALER_RESCUE_INDUSTRY = "Certified Dealer (Rescue + Industry)"
    CERTIFIED_DEALER_RESCUE_SPECIAL_TACTICS_INDUSTRY = "Certified Dealer (Rescue + Special Tactics + Ind.)"
    CERTIFIED_DEALER_RESCUE_SPECIAL_TACTICS = "Certified Dealer (Rescue + Special Tactics)"
    CERTIFIED_DEALER_RESCUE = "Certified Dealer (Rescue)"
    DEALER_INDUSTRY = "Dealer (Industry)"
    DEALER_RESCUE_INDUSTRY = "Dealer (Rescue + Industry)"
    DEALER_RESCUE_SPECIAL_TACTICS_INDUSTRY = "Dealer (Rescue + Special Tactics + Industry)"
    DEALER_RESCUE_SPECIAL_TACTICS = "Dealer (Rescue + Special Tactics)"
    DEALER_RESCUE = "Dealer (Rescue)"
    DEALER_SPECIAL_TACTICS = "Dealer (Special Tactics)"
    PUBLIC_END_USER_INDUSTRY = "Public End User (Industry)"
    PUBLIC_END_USER_RESCUE_SPECIAL_TACTICS = "Public End User (Rescue + Special Tactics)"


class PropositionCategories(str, Enum):
    Industry = "Industry"
    Rescue = "Rescue"


class CentixLanguageIds(Enum):
    NL = 1
    EN = 2
    DE = 25
    DEFAULT = EN
