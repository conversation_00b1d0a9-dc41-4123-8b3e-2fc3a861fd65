from src.data.local_file_storage_repository import LocalFileStorageRepository
from src.domain.models.sync_config import SyncConfig
from src.domain.models.sync_config import SyncType
from src.interactors.sync_interactor import SyncInteractor
from src.repository_factories.datahub_to_centix_repository_factory import DataHubToCentixRepositoryFactory
from src.repository_factories.datahub_to_woocommerce_repository_factory import DataHubToWoocommerceRepositoryFactory
from src.shared.env import Env


class SyncInteractorFactory:
    @staticmethod
    def build_interactor(sync_config: SyncConfig, rate_limited: bool = True, settings: Env = Env()) -> SyncInteractor:
        factory_map = {
            SyncType.CENTIX: DataHubToCentixRepositoryFactory,
            SyncType.WOOCOMMERCE: DataHubToWoocommerceRepositoryFactory,
        }

        repository_factory_cls = factory_map.get(sync_config.sync_type)
        if not repository_factory_cls:
            raise ValueError(f"Invalid sync type: {sync_config.sync_type}")

        repository_factory = repository_factory_cls(settings, rate_limited)

        return SyncInteractor(
            sync_config=sync_config,
            source_product_repository=repository_factory.create_source_repository(),
            target_product_repository=repository_factory.create_target_product_repository(),
            target_category_repository=repository_factory.create_target_category_repository(),
            local_storage_repository=LocalFileStorageRepository(settings),
        )
