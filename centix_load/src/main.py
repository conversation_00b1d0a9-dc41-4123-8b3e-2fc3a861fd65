import logging
from logging import Handler
from uuid import UUID

import sentry_sdk
from sentry_sdk.integrations.logging import LoggingIntegration

from src.domain.models.sync_config import SyncConfig
from src.domain.models.sync_config import SyncType
from src.shared.env import Env
from src.sync_interactor_factory import SyncInteractorFactory

env = Env()

SERVICE_PORTAL_PROPOSITION_ID = UUID("17ef3b47-e729-42b8-9888-6a1350e835f0")
CORPORATE_WEBSITE_PROPOSITION_ID = UUID("8e4d5835-a993-4cc1-b69f-5aeb6d7b0edc")
CORPORATE_WEBSITE_PROPOSITION_ID_2025 = UUID("a667fbca-b868-4471-97e9-e9fbc6bc4079")  # Still in progress

if env.stage != "LOCAL":
    sentry_logging = LoggingIntegration(
        level=logging.INFO,
        event_level=logging.ERROR,
    )

    sentry_sdk.init(
        dsn=env.sentry_dsn,
        traces_sample_rate=0.1,
        profiles_sample_rate=0.1,
        environment=env.stage,
        integrations=[sentry_logging],
    )
    sentry_sdk.set_tag("application", "centix")


def configure_logging(stream: bool = True, file: bool = False) -> None:
    handlers: list[Handler] = []
    if stream:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(env.logging_level)
        handlers.append(console_handler)
    if file:
        file_handler = logging.FileHandler("centix_load.log")
        file_handler.setLevel(env.logging_level)
        handlers.append(file_handler)
    logging.basicConfig(handlers=handlers, level=env.logging_level, format="%(asctime)s %(levelname)s %(message)s")
    logging.info("Application started")


def main():
    configure_logging()
    # TODO: Hardcode Centix till Woocommerce is ready.
    sync_config = SyncConfig(
        proposition_id=CORPORATE_WEBSITE_PROPOSITION_ID,
        sync_type=SyncType.WOOCOMMERCE,
    )

    logging.info(f"Running {sync_config.sync_type.value} Sync")
    interactor = SyncInteractorFactory.build_interactor(sync_config=sync_config, settings=env)
    interactor.execute()


if __name__ == "__main__":
    main()
