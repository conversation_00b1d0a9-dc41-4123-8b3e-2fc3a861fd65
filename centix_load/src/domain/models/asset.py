from datetime import date, datetime

from humps import camelize
from pydantic import BaseModel as PydanticBaseModel
from pydantic import ConfigDict, Field

from src.shared.enum import LanguageEnum


class BaseModel(PydanticBaseModel):
    model_config = ConfigDict(from_attributes=True, alias_generator=camelize, populate_by_name=True)


class Id(BaseModel):
    id: int


class LanguageLabelTranslation(BaseModel):
    language: LanguageEnum
    label: str


class FileLanguage(BaseModel):
    language: LanguageEnum


class File(BaseModel):
    char: str
    file_name: str
    size: int
    cid: str
    type: str
    url: str | None = None
    expiry_url: str
    bref: str = Field(alias="BREF")
    change_date: datetime | None = None
    languages: list[FileLanguage] | None = None
    countries: list[str] | None = None


class TargetGroup(Id, BaseModel):
    name: str
    from_date: date
    until_date: date

    @property
    def is_active(self) -> bool:
        return self.from_date <= date.today() <= self.until_date


class Segmentation(BaseModel):
    target_groups: list[TargetGroup] = []
    brands: list = []
    markets: list = []

    def get_target_group_by_name(self, name: str) -> TargetGroup | None:
        return next(
            (target_group for target_group in self.target_groups if target_group.name == name),
            None,
        )


class AssetDetails(BaseModel):
    udai: str = Field(alias="UDAI")
    reference_nr: int
    change_date: datetime
    type_id: int
    sub_type_id: int
    translations: list[LanguageLabelTranslation]
    files: list[File]
    segmentation: Segmentation

    def get_translation(self, language: LanguageEnum) -> str | None:
        return next(
            (translation.label for translation in self.translations if translation.language == language),
            None,
        )

    def get_first_file_for_char(self, char: str) -> File | None:
        return next((file for file in self.files if file.char == char), None)

    def get_file_for_char_and_language(self, char: str, language: LanguageEnum) -> File | None:
        return next(
            (
                file
                for file in self.files
                if file.char == char and file.languages and language in [lang.language for lang in file.languages]
            ),
            None,
        )
