from datetime import datetime
from enum import IntEnum

from pydantic import BaseModel


class FileUriType(IntEnum):
    URL = 0
    UNC_PATH = 1
    RELATIVE_PATH = 2


class Document(BaseModel):
    """
    Document model representing a document.
    """

    id: str | None = None
    auto_id: int | None = None
    language_id: int | None = None
    descr: str | None = None
    document_kind_id: int | None = None
    file_uri_type: FileUriType | None = None
    file_name: str | None = None
    file_extension: str | None = None
    file_size: int | None = None
    time_stamp: datetime | None = None

    asset_udai: str | None = None
    sanitized_file_name: str | None = None
    is_public_media: bool | None = None
