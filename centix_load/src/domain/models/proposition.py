from datetime import datetime

from humps import camelize
from pydantic import BaseModel as PydanticBaseModel
from pydantic import ConfigDict

from src.shared.enum import LanguageEnum


class BaseModel(PydanticBaseModel):
    model_config = ConfigDict(from_attributes=True, alias_generator=camelize, populate_by_name=True)


# Product model
class ProductItem(BaseModel):
    umid: str
    pmid: str
    change_date: datetime
    digital_life_cycle_status: str
    sort_index: int
    star: bool


class CategoryTranslation(BaseModel):
    language: LanguageEnum
    label: str


# Category model
class Category(BaseModel):
    guid: str
    id: int
    sort_index: int
    products: list[ProductItem]
    translations: list[CategoryTranslation]
    sub_categories: list["Category"]

    model_config = ConfigDict(arbitrary_types_allowed=True)


# Main Proposition Model
class Proposition(BaseModel):
    guid: str
    categories: list[Category]
