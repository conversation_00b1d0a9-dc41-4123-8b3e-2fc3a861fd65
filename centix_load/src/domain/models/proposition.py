from datetime import datetime
from typing import List

from humps import camelize
from pydantic import BaseModel as PydanticBaseModel
from pydantic import ConfigDict

from src.shared.enum import LanguageEnum


class BaseModel(PydanticBaseModel):
    model_config = ConfigDict(from_attributes=True, alias_generator=camelize, populate_by_name=True)


# Product model
class ProductItem(BaseModel):
    umid: str
    pmid: str
    change_date: datetime
    digital_life_cycle_status: str
    sort_index: int
    star: bool


class CategoryTranslation(BaseModel):
    language: LanguageEnum
    label: str


# Category model
class Category(BaseModel):
    guid: str
    id: int
    sort_index: int
    products: List[ProductItem]
    translations: List[CategoryTranslation]
    sub_categories: List["Category"]

    model_config = ConfigDict(arbitrary_types_allowed=True)


# Main Proposition Model
class Proposition(BaseModel):
    guid: str
    categories: List[Category]
