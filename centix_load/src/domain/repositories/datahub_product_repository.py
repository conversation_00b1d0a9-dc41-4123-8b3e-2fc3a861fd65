from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any

from src.domain.models.asset import AssetDetails
from src.domain.models.directory_file import DirectoryFile
from src.domain.models.product import Product
from src.domain.models.proposition import Proposition


class SourceProductRepository(ABC):
    @abstractmethod
    def get_file_contents(self, file_name: str) -> Any:
        """Fetches file contents."""

    @abstractmethod
    def get_directory_files(self, sub_directory: str, modified_since: datetime | None = None) -> list[DirectoryFile]:
        """Fetches a list of files from a directory (root by default)."""
        raise NotImplementedError

    @abstractmethod
    def get_last_modified_header(self, file_path: str) -> str:
        """Fetches the last modified header of a file."""
        raise NotImplementedError

    @abstractmethod
    def get_product_by_id(self, product_id: str) -> Product:
        """Fetches a product by its ID and returns a domain model Product."""
        raise NotImplementedError

    @abstractmethod
    def get_proposition_by_id(self, proposition_id: str) -> Proposition:
        """Fetches a proposition by its ID and returns a domain model Proposition."""
        raise NotImplementedError

    @abstractmethod
    def get_asset_by_id(self, asset_id: str) -> AssetDetails:
        """Fetches an asset by its ID and returns a domain model AssetDetails."""
        raise NotImplementedError
