from abc import ABC, abstractmethod

from src.domain.models.sync_config import SyncConfig
from src.domain.models.sync_metadata.sync_metadata_directory_file import SyncMetaDataDirectoryFile


class AbstractFileStorageRepository(ABC):
    """
    Abstract base class for file storage repositories.
    """

    @abstractmethod
    def write_sync_metadata(self, sync_metadata: SyncMetaDataDirectoryFile, sync_config: SyncConfig) -> None:
        """
        Write the sync metadata to the file system.
        """
        raise NotImplementedError

    @abstractmethod
    def read_sync_metadata(self, sync_config: SyncConfig) -> SyncMetaDataDirectoryFile | None:
        """
        Read the sync metadata from the file system.
        """
        raise NotImplementedError
