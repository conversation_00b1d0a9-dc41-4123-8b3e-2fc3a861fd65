SF:src/__init__.py
end_of_record
SF:src/data/__init__.py
end_of_record
SF:src/data/centix_document_datasource.py
DA:1,1
DA:2,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:13,1
DA:14,1
DA:15,1
DA:16,1
DA:17,1
DA:18,1
DA:19,1
DA:20,1
DA:21,1
DA:22,1
DA:23,1
DA:24,1
DA:25,1
DA:26,1
DA:29,1
DA:30,1
DA:32,1
DA:33,1
DA:41,1
DA:44,1
DA:45,1
DA:46,1
DA:47,1
DA:48,1
DA:50,1
DA:51,1
DA:53,1
DA:54,1
DA:55,1
DA:56,1
DA:57,0
DA:59,1
DA:61,0
DA:62,0
DA:63,0
DA:64,0
DA:66,1
DA:68,1
DA:69,1
DA:70,1
DA:72,1
DA:74,1
DA:76,1
DA:78,1
DA:80,1
DA:82,1
DA:83,1
DA:85,1
DA:87,1
DA:88,1
DA:90,1
DA:91,1
DA:92,1
DA:94,1
DA:95,1
DA:96,0
DA:97,0
DA:98,0
DA:100,1
DA:101,1
DA:102,0
DA:103,0
DA:106,1
DA:107,1
DA:111,1
DA:113,1
DA:115,1
DA:116,1
DA:117,1
DA:118,1
DA:120,1
DA:122,1
DA:123,1
DA:124,1
DA:125,1
DA:126,1
DA:127,1
DA:128,1
DA:130,1
DA:131,0
DA:132,0
DA:134,1
DA:142,1
DA:143,1
DA:144,1
DA:146,1
DA:147,1
DA:148,1
DA:150,1
DA:151,1
DA:152,1
DA:154,1
DA:156,1
DA:165,1
DA:166,1
DA:167,1
DA:168,1
DA:169,1
DA:170,1
DA:172,1
DA:173,1
DA:174,1
DA:176,1
DA:179,1
DA:187,1
DA:189,1
DA:191,1
DA:192,1
DA:193,1
DA:194,1
DA:195,1
DA:196,1
DA:197,1
DA:199,1
DA:207,1
DA:208,1
DA:211,1
DA:212,1
DA:213,1
DA:216,1
DA:217,1
DA:218,1
DA:221,0
DA:222,0
DA:224,1
DA:225,0
DA:226,0
DA:228,1
DA:234,1
DA:235,1
DA:236,1
DA:237,1
DA:238,1
DA:239,1
DA:240,1
DA:241,1
DA:243,1
DA:251,1
DA:253,1
DA:255,1
DA:256,1
DA:257,1
DA:259,1
DA:267,1
DA:268,1
DA:269,1
DA:271,0
DA:272,0
DA:274,1
DA:281,1
DA:282,1
DA:283,1
DA:285,1
DA:286,1
DA:287,1
DA:289,0
DA:290,0
DA:292,1
DA:294,0
DA:295,0
DA:296,1
DA:298,0
DA:299,0
DA:300,0
DA:302,0
DA:304,0
DA:306,1
DA:313,1
DA:314,1
DA:315,1
DA:317,1
DA:319,1
DA:320,1
DA:322,1
DA:323,1
DA:324,1
DA:325,1
DA:326,1
DA:328,1
DA:330,1
DA:331,1
DA:333,1
DA:334,0
DA:336,0
DA:337,0
DA:339,0
DA:342,0
DA:344,0
DA:345,0
DA:347,1
DA:349,1
DA:350,1
DA:351,1
DA:352,1
DA:353,1
DA:354,1
DA:355,1
LF:207
LH:173
FN:44,48,CentixDocumentDatasource.__init__
FNDA:1,CentixDocumentDatasource.__init__
FN:51,57,CentixDocumentDatasource.get_document_kind_by_id
FNDA:1,CentixDocumentDatasource.get_document_kind_by_id
FN:59,64,CentixDocumentDatasource.get_document_by_unique_id
FNDA:0,CentixDocumentDatasource.get_document_by_unique_id
FN:66,70,CentixDocumentDatasource.create_document
FNDA:1,CentixDocumentDatasource.create_document
FN:72,74,CentixDocumentDatasource.delete_document_by_aid
FNDA:1,CentixDocumentDatasource.delete_document_by_aid
FN:76,78,CentixDocumentDatasource.link_document_to_product
FNDA:1,CentixDocumentDatasource.link_document_to_product
FN:80,83,CentixDocumentDatasource.get_product_documents
FNDA:1,CentixDocumentDatasource.get_product_documents
FN:85,111,CentixDocumentDatasource.upsert
FNDA:1,CentixDocumentDatasource.upsert
FN:113,118,CentixDocumentDatasource._is_supported_asset
FNDA:1,CentixDocumentDatasource._is_supported_asset
FN:120,132,CentixDocumentDatasource._collect_document_kind_by_role
FNDA:1,CentixDocumentDatasource._collect_document_kind_by_role
FN:134,154,CentixDocumentDatasource._tech_spec_sheet_exception
FNDA:1,CentixDocumentDatasource._tech_spec_sheet_exception
FN:156,187,CentixDocumentDatasource._process_document_for_languages
FNDA:1,CentixDocumentDatasource._process_document_for_languages
FN:189,197,CentixDocumentDatasource._get_document_file
FNDA:1,CentixDocumentDatasource._get_document_file
FN:199,226,CentixDocumentDatasource.upsert_document_from_url
FNDA:1,CentixDocumentDatasource.upsert_document_from_url
FN:228,241,CentixDocumentDatasource._find_existing_document
FNDA:1,CentixDocumentDatasource._find_existing_document
FN:243,251,CentixDocumentDatasource._handle_existing_document
FNDA:1,CentixDocumentDatasource._handle_existing_document
FN:253,257,CentixDocumentDatasource._get_document_by_id
FNDA:1,CentixDocumentDatasource._get_document_by_id
FN:259,272,CentixDocumentDatasource._handle_existing_document_linked
FNDA:1,CentixDocumentDatasource._handle_existing_document_linked
FN:274,290,CentixDocumentDatasource._update_document
FNDA:1,CentixDocumentDatasource._update_document
FN:292,304,CentixDocumentDatasource._handle_new_document
FNDA:1,CentixDocumentDatasource._handle_new_document
FN:306,326,CentixDocumentDatasource._replace_document
FNDA:1,CentixDocumentDatasource._replace_document
FN:328,345,CentixDocumentDatasource._check_document_timestamp
FNDA:1,CentixDocumentDatasource._check_document_timestamp
FN:347,355,CentixDocumentDatasource._remove_unused_documents
FNDA:1,CentixDocumentDatasource._remove_unused_documents
FNF:23
FNH:22
end_of_record
SF:src/data/centix_product_repository_impl.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:13,1
DA:14,1
DA:15,1
DA:16,1
DA:17,1
DA:18,1
DA:19,1
DA:20,1
DA:21,1
DA:22,1
DA:23,1
DA:24,1
DA:25,1
DA:27,1
DA:28,1
DA:29,1
DA:30,1
DA:31,1
DA:32,1
DA:33,1
DA:34,1
DA:37,1
DA:38,1
DA:46,1
DA:47,1
DA:48,1
DA:49,1
DA:50,1
DA:51,1
DA:52,1
DA:53,1
DA:55,1
DA:56,1
DA:58,1
DA:59,1
DA:60,1
DA:61,0
DA:62,0
DA:63,0
DA:65,1
DA:66,1
DA:67,1
DA:69,1
DA:70,1
DA:72,1
DA:73,1
DA:74,1
DA:75,1
DA:76,1
DA:78,1
DA:79,1
DA:81,1
DA:82,1
DA:83,1
DA:84,1
DA:85,1
DA:87,1
DA:88,1
DA:90,1
DA:91,1
DA:92,1
DA:93,1
DA:94,1
DA:96,1
DA:97,1
DA:99,0
DA:100,0
DA:101,0
DA:102,0
DA:103,1
DA:105,1
DA:106,1
DA:108,0
DA:109,0
DA:110,0
DA:111,0
DA:112,1
DA:114,1
DA:115,1
DA:117,1
DA:119,1
DA:120,1
DA:121,0
DA:122,1
DA:124,1
DA:125,1
DA:126,0
DA:127,0
DA:128,0
DA:129,0
DA:131,1
DA:134,1
DA:135,1
DA:136,1
DA:137,1
DA:138,0
DA:139,0
DA:140,0
DA:141,0
DA:143,1
DA:144,1
DA:146,1
DA:149,1
DA:150,1
DA:152,1
DA:153,1
DA:155,1
DA:156,1
DA:158,1
DA:159,1
DA:161,1
DA:162,1
DA:164,1
DA:167,1
DA:169,1
DA:170,1
DA:172,1
DA:173,1
DA:175,1
DA:176,1
DA:178,1
DA:179,1
DA:181,1
DA:190,1
DA:191,1
DA:192,1
DA:194,1
DA:195,1
DA:196,1
DA:198,1
DA:200,1
DA:202,1
DA:203,1
DA:205,1
DA:206,0
DA:207,0
DA:210,1
DA:211,1
DA:218,1
DA:220,1
DA:222,1
DA:224,1
DA:225,0
DA:226,1
DA:227,0
DA:228,1
DA:229,0
DA:230,0
DA:231,0
DA:232,1
DA:234,1
DA:236,1
DA:243,1
DA:244,1
DA:245,1
DA:247,1
DA:248,1
DA:250,1
DA:251,1
DA:254,1
DA:256,1
DA:259,1
DA:261,1
DA:262,1
DA:264,1
DA:265,1
DA:268,1
DA:271,1
DA:272,1
DA:275,1
DA:276,1
LF:182
LH:155
FN:38,53,CentixProductRepositoryImpl.__init__
FNDA:1,CentixProductRepositoryImpl.__init__
FN:55,63,CentixProductRepositoryImpl.get_product_by_article_number
FNDA:1,CentixProductRepositoryImpl.get_product_by_article_number
FN:65,67,CentixProductRepositoryImpl.patch_product_properties
FNDA:1,CentixProductRepositoryImpl.patch_product_properties
FN:70,76,CentixProductRepositoryImpl.get_brand_by_id
FNDA:1,CentixProductRepositoryImpl.get_brand_by_id
FN:79,85,CentixProductRepositoryImpl.get_object_type_by_id
FNDA:1,CentixProductRepositoryImpl.get_object_type_by_id
FN:88,94,CentixProductRepositoryImpl.get_miplan_by_id
FNDA:1,CentixProductRepositoryImpl.get_miplan_by_id
FN:97,103,CentixProductRepositoryImpl.get_product_group_by_id
FNDA:1,CentixProductRepositoryImpl.get_product_group_by_id
FN:106,112,CentixProductRepositoryImpl.get_product_category_by_id
FNDA:1,CentixProductRepositoryImpl.get_product_category_by_id
FN:114,129,CentixProductRepositoryImpl.get_brand_type_by_id
FNDA:1,CentixProductRepositoryImpl.get_brand_type_by_id
FN:131,141,CentixProductRepositoryImpl.get_default_mi_plan_for_brand_type
FNDA:1,CentixProductRepositoryImpl.get_default_mi_plan_for_brand_type
FN:143,144,CentixProductRepositoryImpl.delete_default_mi_plan
FNDA:1,CentixProductRepositoryImpl.delete_default_mi_plan
FN:146,150,CentixProductRepositoryImpl.create_default_mi_plan
FNDA:1,CentixProductRepositoryImpl.create_default_mi_plan
FN:152,153,CentixProductRepositoryImpl.create_brand_type
FNDA:1,CentixProductRepositoryImpl.create_brand_type
FN:155,156,CentixProductRepositoryImpl.update_brand_type
FNDA:1,CentixProductRepositoryImpl.update_brand_type
FN:158,159,CentixProductRepositoryImpl.delete_brand_type
FNDA:1,CentixProductRepositoryImpl.delete_brand_type
FN:161,162,CentixProductRepositoryImpl.create_product
FNDA:1,CentixProductRepositoryImpl.create_product
FN:164,167,CentixProductRepositoryImpl.update_product
FNDA:1,CentixProductRepositoryImpl.update_product
FN:169,170,CentixProductRepositoryImpl.delete_product
FNDA:1,CentixProductRepositoryImpl.delete_product
FN:172,173,CentixProductRepositoryImpl.patch_product_translations
FNDA:1,CentixProductRepositoryImpl.patch_product_translations
FN:175,176,CentixProductRepositoryImpl.update_image_for_product
FNDA:1,CentixProductRepositoryImpl.update_image_for_product
FN:178,179,CentixProductRepositoryImpl.delete_image_for_product
FNDA:1,CentixProductRepositoryImpl.delete_image_for_product
FN:181,198,CentixProductRepositoryImpl._verify_and_upsert_brand_type_and_mi_plan
FNDA:1,CentixProductRepositoryImpl._verify_and_upsert_brand_type_and_mi_plan
FN:200,218,CentixProductRepositoryImpl._upsert_brand_type
FNDA:1,CentixProductRepositoryImpl._upsert_brand_type
FN:220,234,CentixProductRepositoryImpl._upsert_default_mi_plan
FNDA:1,CentixProductRepositoryImpl._upsert_default_mi_plan
FN:236,276,CentixProductRepositoryImpl.upsert
FNDA:1,CentixProductRepositoryImpl.upsert
FNF:25
FNH:25
end_of_record
SF:src/data/datahub_sync_force_data_source_repository.py
DA:1,1
DA:2,1
DA:3,1
DA:4,1
DA:5,1
DA:7,1
DA:8,1
DA:9,1
DA:11,1
DA:12,1
DA:13,1
DA:14,1
DA:15,1
DA:16,1
DA:17,1
DA:18,1
DA:19,1
DA:20,1
DA:21,1
DA:22,1
DA:23,1
DA:24,1
DA:29,1
DA:30,1
DA:31,1
DA:32,1
DA:34,1
DA:35,1
DA:40,1
DA:41,1
DA:42,1
DA:43,1
DA:44,1
DA:46,1
DA:48,1
DA:49,1
DA:50,1
DA:52,0
DA:54,1
DA:55,1
DA:58,1
DA:61,1
DA:66,1
DA:68,1
DA:69,1
DA:70,1
DA:71,1
DA:73,1
DA:74,0
DA:75,0
DA:76,0
DA:77,0
DA:79,1
DA:89,1
DA:92,1
DA:95,1
DA:97,1
DA:107,1
DA:110,1
DA:113,1
DA:115,1
DA:125,1
DA:127,1
DA:129,1
DA:131,1
LF:65
LH:60
FN:30,32,DatahubProductRepositoryImpl.__init__
FNDA:1,DatahubProductRepositoryImpl.__init__
FN:34,44,DatahubProductRepositoryImpl.__fetch_data
FNDA:1,DatahubProductRepositoryImpl.__fetch_data
FN:46,64,DatahubProductRepositoryImpl.get_directory_files
FNDA:1,DatahubProductRepositoryImpl.get_directory_files
FN:66,71,DatahubProductRepositoryImpl.get_file_contents
FNDA:1,DatahubProductRepositoryImpl.get_file_contents
FN:73,77,DatahubProductRepositoryImpl.get_last_modified_header
FNDA:0,DatahubProductRepositoryImpl.get_last_modified_header
FN:79,95,DatahubProductRepositoryImpl.get_product_by_id
FNDA:1,DatahubProductRepositoryImpl.get_product_by_id
FN:97,113,DatahubProductRepositoryImpl.get_proposition_by_id
FNDA:1,DatahubProductRepositoryImpl.get_proposition_by_id
FN:115,131,DatahubProductRepositoryImpl.get_asset_by_id
FNDA:1,DatahubProductRepositoryImpl.get_asset_by_id
FNF:8
FNH:7
end_of_record
SF:src/data/local_file_storage_repository.py
DA:1,1
DA:2,1
DA:3,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:11,1
DA:17,1
DA:18,1
DA:19,1
DA:21,1
DA:22,1
DA:24,1
DA:28,1
DA:29,1
DA:30,1
DA:31,1
DA:32,1
DA:33,0
DA:34,0
DA:36,1
DA:40,1
DA:41,1
DA:42,1
DA:43,1
DA:44,0
DA:47,1
DA:48,1
LF:29
LH:26
FN:17,19,LocalFileStorageRepository.__init__
FNDA:1,LocalFileStorageRepository.__init__
FN:21,22,LocalFileStorageRepository._sync_metadata_filename
FNDA:1,LocalFileStorageRepository._sync_metadata_filename
FN:24,34,LocalFileStorageRepository.write_sync_metadata
FNDA:1,LocalFileStorageRepository.write_sync_metadata
FN:36,48,LocalFileStorageRepository.read_sync_metadata
FNDA:1,LocalFileStorageRepository.read_sync_metadata
FNF:4
FNH:4
end_of_record
SF:src/data/mappers/__init__.py
end_of_record
SF:src/data/mappers/centix_document_kind_mapper.py
DA:1,1
DA:3,1
DA:6,1
DA:11,1
DA:12,1
DA:22,1
LF:6
LH:6
FN:12,25,DocumentKindMapper.to_domain
FNDA:1,DocumentKindMapper.to_domain
FNF:1
FNH:1
end_of_record
SF:src/data/mappers/centix_document_mapper.py
DA:1,1
DA:2,1
DA:3,1
DA:5,1
DA:6,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:13,1
DA:14,1
DA:15,1
DA:16,1
DA:18,1
DA:19,1
DA:22,1
DA:27,1
DA:28,1
DA:50,1
DA:51,1
DA:52,1
DA:53,1
DA:54,1
DA:55,0
DA:56,0
DA:58,1
DA:59,1
DA:61,1
DA:62,1
DA:65,1
DA:77,1
DA:79,1
DA:80,1
DA:91,1
DA:92,1
DA:93,0
DA:94,0
DA:95,0
DA:97,1
DA:99,1
DA:101,1
DA:107,1
DA:113,1
DA:124,1
DA:126,1
DA:127,1
DA:129,1
DA:130,1
DA:131,1
DA:132,1
DA:134,1
DA:135,1
DA:145,1
DA:146,1
DA:147,1
DA:148,1
DA:149,0
DA:150,0
DA:152,1
LF:60
LH:53
FN:28,77,DocumentMapper.create_document_from_assets
FNDA:1,DocumentMapper.create_document_from_assets
FN:80,124,DocumentMapper.to_centix_create_document_from_url
FNDA:1,DocumentMapper.to_centix_create_document_from_url
FN:127,132,DocumentMapper.sanitize_desired_filename
FNDA:1,DocumentMapper.sanitize_desired_filename
FN:135,163,DocumentMapper.to_domain
FNDA:1,DocumentMapper.to_domain
FNF:4
FNH:4
end_of_record
SF:src/data/mappers/centix_product_mapper.py
DA:1,1
DA:2,1
DA:3,1
DA:4,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:14,1
DA:15,1
DA:16,1
DA:17,1
DA:18,1
DA:19,1
DA:21,1
DA:24,1
DA:25,1
DA:34,1
DA:35,1
DA:44,1
DA:46,1
DA:47,1
DA:48,1
DA:49,1
DA:51,1
DA:52,1
DA:53,1
DA:55,1
DA:57,1
DA:58,1
DA:67,1
DA:68,1
DA:85,1
DA:87,1
DA:88,1
DA:89,1
DA:90,1
DA:92,1
DA:93,1
DA:94,1
DA:96,1
DA:98,1
DA:99,1
DA:101,1
DA:102,1
DA:103,1
DA:104,1
DA:106,1
DA:107,1
DA:108,1
DA:110,1
DA:111,1
DA:112,1
DA:113,1
DA:114,1
DA:115,1
DA:116,1
DA:117,1
DA:118,1
DA:119,1
DA:120,1
DA:121,1
DA:122,1
DA:123,1
DA:126,1
DA:128,1
DA:129,1
DA:131,1
DA:132,1
DA:133,1
DA:134,1
DA:136,1
DA:137,1
DA:141,1
DA:145,1
DA:146,1
DA:147,1
DA:149,1
DA:151,1
DA:152,1
DA:156,1
DA:162,1
DA:163,1
DA:165,1
DA:166,1
DA:167,1
DA:168,1
DA:170,1
DA:171,1
DA:177,1
DA:186,1
DA:187,1
DA:191,1
LF:96
LH:96
FN:35,55,ProductMapper.to_centix_read_sales_product
FNDA:1,ProductMapper.to_centix_read_sales_product
FN:58,96,ProductMapper.to_centix_update_sales_product
FNDA:1,ProductMapper.to_centix_update_sales_product
FN:99,104,ProductMapper.get_centix_product_descr2
FNDA:1,ProductMapper.get_centix_product_descr2
FN:107,126,ProductMapper.to_centix_sales_product_properties
FNDA:1,ProductMapper.to_centix_sales_product_properties
FN:129,134,ProductMapper.to_centix_sales_product_translations
FNDA:1,ProductMapper.to_centix_sales_product_translations
FN:137,149,ProductMapper._get_translations_for_language
FNDA:1,ProductMapper._get_translations_for_language
FN:152,160,ProductMapper._create_translation_item
FNDA:1,ProductMapper._create_translation_item
FN:163,168,ProductMapper._get_long_description
FNDA:1,ProductMapper._get_long_description
FN:171,184,ProductMapper.to_centix_brand_type
FNDA:1,ProductMapper.to_centix_brand_type
FN:187,198,ProductMapper.to_brand_type_default_mi_plan
FNDA:1,ProductMapper.to_brand_type_default_mi_plan
FNF:10
FNH:10
end_of_record
SF:src/data/mappers/sync_force_asset_mapper.py
DA:1,1
DA:2,1
DA:5,1
DA:8,1
DA:9,1
DA:20,1
DA:21,1
DA:22,1
DA:23,1
DA:24,1
DA:25,1
LF:11
LH:11
FN:9,25,SyncForceAssetMapper.to_domain_asset
FNDA:1,SyncForceAssetMapper.to_domain_asset
FNF:1
FNH:1
end_of_record
SF:src/data/mappers/sync_force_directory_file_mapper.py
DA:1,1
DA:2,1
DA:5,1
DA:8,1
DA:9,1
DA:19,1
DA:20,1
LF:7
LH:7
FN:9,20,SyncForceDirectoryFileMapper.to_domain_directory_file
FNDA:1,SyncForceDirectoryFileMapper.to_domain_directory_file
FNF:1
FNH:1
end_of_record
SF:src/data/mappers/sync_force_product_mapper.py
DA:1,1
DA:2,1
DA:5,1
DA:8,1
DA:9,1
DA:19,1
DA:20,1
LF:7
LH:7
FN:9,20,SyncForceProductMapper.to_domain_product
FNDA:1,SyncForceProductMapper.to_domain_product
FNF:1
FNH:1
end_of_record
SF:src/data/mappers/sync_force_proposition_mapper.py
DA:1,1
DA:2,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:12,1
DA:15,1
DA:16,1
DA:18,1
DA:25,1
DA:26,1
DA:27,1
DA:36,1
DA:37,1
DA:38,1
DA:40,1
DA:41,1
DA:42,1
LF:22
LH:22
FN:16,23,SyncForcePropositionMapper.to_domain_proposition
FNDA:1,SyncForcePropositionMapper.to_domain_proposition
FN:26,34,SyncForcePropositionMapper._map_category
FNDA:1,SyncForcePropositionMapper._map_category
FN:37,38,SyncForcePropositionMapper._map_translation
FNDA:1,SyncForcePropositionMapper._map_translation
FN:41,49,SyncForcePropositionMapper._map_product
FNDA:1,SyncForcePropositionMapper._map_product
FNF:4
FNH:4
end_of_record
SF:src/data/mappers/sync_force_proposition_response_mapper.py
DA:1,1
DA:2,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:13,1
DA:14,1
DA:15,1
DA:16,1
DA:29,1
DA:30,1
DA:31,1
DA:36,1
DA:37,1
DA:38,1
DA:47,1
DA:48,1
DA:49,1
DA:55,1
DA:56,1
DA:57,1
DA:81,1
DA:82,1
DA:83,1
DA:87,1
DA:88,1
DA:89,1
DA:91,1
DA:92,1
DA:93,1
DA:102,1
DA:103,1
DA:104,1
DA:111,1
DA:112,1
DA:113,0
LF:41
LH:40
FN:15,27,SyncForcePropositionResponseMapper.to_model
FNDA:1,SyncForcePropositionResponseMapper.to_model
FN:30,34,SyncForcePropositionResponseMapper._map_translation
FNDA:1,SyncForcePropositionResponseMapper._map_translation
FN:37,45,SyncForcePropositionResponseMapper._map_product_item
FNDA:1,SyncForcePropositionResponseMapper._map_product_item
FN:48,53,SyncForcePropositionResponseMapper._map_content
FNDA:1,SyncForcePropositionResponseMapper._map_content
FN:56,79,SyncForcePropositionResponseMapper._map_category
FNDA:1,SyncForcePropositionResponseMapper._map_category
FN:82,85,SyncForcePropositionResponseMapper._map_title
FNDA:1,SyncForcePropositionResponseMapper._map_title
FN:88,89,SyncForcePropositionResponseMapper._map_language
FNDA:1,SyncForcePropositionResponseMapper._map_language
FN:92,100,SyncForcePropositionResponseMapper._map_market
FNDA:1,SyncForcePropositionResponseMapper._map_market
FN:103,109,SyncForcePropositionResponseMapper._map_text_role
FNDA:1,SyncForcePropositionResponseMapper._map_text_role
FN:112,118,SyncForcePropositionResponseMapper._map_digital_asset_role
FNDA:0,SyncForcePropositionResponseMapper._map_digital_asset_role
FNF:10
FNH:9
end_of_record
SF:src/data/models/__init__.py
end_of_record
SF:src/data/models/centix/__init__.py
end_of_record
SF:src/data/models/centix/product_properties.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:10,1
DA:11,1
DA:13,1
DA:14,1
DA:15,1
DA:16,1
DA:17,1
DA:18,1
DA:20,1
DA:21,1
DA:22,1
DA:23,1
DA:24,1
DA:25,1
LF:20
LH:20
FN:21,25,ProductProperties.truncate_model
FNDA:1,ProductProperties.truncate_model
FNF:1
FNH:1
end_of_record
SF:src/data/models/sync_force/__init__.py
end_of_record
SF:src/data/models/sync_force/sync_force_directory_file.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:8,1
DA:9,1
DA:11,1
DA:12,1
DA:13,1
LF:9
LH:9
end_of_record
SF:src/data/models/sync_force/syncforce_asset_schema.py
DA:1,1
DA:2,1
DA:3,1
DA:4,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:12,1
DA:15,1
DA:16,1
DA:19,1
DA:20,1
DA:23,1
DA:24,1
DA:25,1
DA:28,1
DA:29,1
DA:30,1
DA:33,1
DA:34,1
DA:35,1
DA:38,1
DA:39,1
DA:40,1
DA:43,1
DA:44,1
DA:45,1
DA:48,1
DA:49,1
DA:50,1
DA:52,1
DA:53,1
DA:54,0
DA:56,1
DA:57,1
DA:58,0
DA:61,1
DA:62,1
DA:63,1
DA:66,1
DA:67,1
DA:68,1
DA:71,1
DA:72,1
DA:75,1
DA:76,1
DA:77,1
DA:78,1
DA:79,1
DA:80,1
DA:81,1
DA:82,1
DA:83,1
DA:84,1
DA:85,1
DA:88,1
DA:89,1
DA:90,1
DA:91,1
DA:93,1
DA:94,1
DA:95,0
DA:98,1
DA:99,1
DA:100,1
DA:101,1
DA:103,1
DA:104,0
DA:110,1
DA:111,1
DA:112,1
DA:113,1
DA:114,1
DA:115,1
DA:116,1
DA:117,1
DA:118,1
DA:120,1
DA:121,0
DA:126,1
DA:127,0
DA:129,1
DA:130,0
LF:85
LH:78
FN:53,54,AssetSubType.extract_plural_translations
FNDA:0,AssetSubType.extract_plural_translations
FN:57,58,AssetSubType.extract_singular_translations
FNDA:0,AssetSubType.extract_singular_translations
FN:94,95,TargetGroup.is_active
FNDA:0,TargetGroup.is_active
FN:103,107,Segmentation.get_target_group_by_name
FNDA:0,Segmentation.get_target_group_by_name
FN:120,124,AssetDetails.get_translation
FNDA:0,AssetDetails.get_translation
FN:126,127,AssetDetails.get_first_file_for_char
FNDA:0,AssetDetails.get_first_file_for_char
FN:129,137,AssetDetails.get_file_for_char_and_language
FNDA:0,AssetDetails.get_file_for_char_and_language
FNF:7
FNH:0
end_of_record
SF:src/data/models/sync_force/syncforce_product_schema.py
DA:1,1
DA:2,1
DA:3,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:10,1
DA:13,1
DA:14,1
DA:17,1
DA:18,1
DA:21,1
DA:22,1
DA:25,1
DA:26,1
DA:27,1
DA:30,1
DA:31,1
DA:32,1
DA:35,1
DA:36,1
DA:37,1
DA:40,1
DA:41,1
DA:42,1
DA:45,1
DA:46,1
DA:49,1
DA:50,1
DA:51,1
DA:54,1
DA:55,1
DA:56,1
DA:57,1
DA:60,1
DA:61,1
DA:62,1
DA:63,1
DA:64,1
DA:65,1
DA:66,1
DA:69,1
DA:70,1
DA:71,1
DA:74,1
DA:75,1
DA:78,1
DA:79,1
DA:82,1
DA:83,1
DA:84,1
DA:86,1
DA:87,0
DA:90,1
DA:91,1
DA:94,1
DA:95,1
DA:97,1
DA:98,0
DA:101,1
DA:104,1
DA:106,1
DA:107,0
DA:110,1
DA:111,1
DA:114,1
DA:115,1
DA:118,1
DA:119,1
DA:120,1
DA:122,1
DA:123,0
DA:128,1
DA:129,1
DA:130,1
DA:132,1
DA:133,0
DA:135,1
DA:136,0
DA:137,0
DA:138,0
DA:141,1
DA:142,1
DA:143,1
DA:144,1
DA:145,1
DA:147,1
DA:148,0
DA:151,1
DA:152,1
DA:153,1
DA:154,1
DA:157,1
DA:158,1
DA:159,1
DA:160,1
DA:161,1
DA:164,1
DA:165,1
DA:166,1
DA:169,1
DA:170,1
DA:171,1
DA:174,1
DA:175,1
DA:176,1
DA:177,1
DA:178,1
DA:180,1
DA:181,0
DA:186,1
DA:187,1
DA:188,1
DA:189,1
DA:190,1
DA:191,1
DA:192,1
DA:195,1
DA:196,1
DA:197,1
DA:200,1
DA:201,1
DA:202,1
DA:203,1
DA:206,1
DA:207,1
DA:208,1
DA:211,1
DA:212,1
DA:213,1
DA:216,1
DA:219,1
DA:220,1
DA:221,1
DA:222,1
DA:223,1
DA:224,1
DA:225,1
DA:226,1
DA:227,1
DA:228,1
DA:229,1
DA:230,1
DA:231,1
DA:232,1
DA:233,1
LF:147
LH:137
FN:86,87,ProductText.get_translation
FNDA:0,ProductText.get_translation
FN:97,98,ProductName.get_translation
FNDA:0,ProductName.get_translation
FN:106,107,Translations.get_translation
FNDA:0,Translations.get_translation
FN:122,125,AsssetSubType.get_translation
FNDA:0,AsssetSubType.get_translation
FN:132,133,ProductAsset.__hash__
FNDA:0,ProductAsset.__hash__
FN:135,138,ProductAsset.__eq__
FNDA:0,ProductAsset.__eq__
FN:147,148,ProductAssets.get_asset_id_for_role
FNDA:0,ProductAssets.get_asset_id_for_role
FN:180,183,AttributeValue.get_translation
FNDA:0,AttributeValue.get_translation
FNF:8
FNH:0
end_of_record
SF:src/data/models/sync_force/syncforce_proposition_schema.py
DA:1,1
DA:2,1
DA:3,1
DA:4,1
DA:6,1
DA:7,1
DA:8,1
DA:11,1
DA:12,1
DA:16,1
DA:17,1
DA:18,1
DA:22,1
DA:23,1
DA:27,1
DA:28,1
DA:32,1
DA:33,1
DA:34,1
DA:35,1
DA:36,1
DA:40,1
DA:41,1
DA:42,1
DA:43,1
DA:44,1
DA:48,1
DA:49,1
DA:50,1
DA:51,1
DA:52,1
DA:56,1
DA:57,1
DA:58,1
DA:59,1
DA:60,1
DA:61,1
DA:62,1
DA:66,1
DA:67,1
DA:68,1
DA:69,1
DA:73,1
DA:74,1
DA:75,1
DA:76,1
DA:77,1
DA:78,1
DA:79,1
DA:80,1
DA:81,1
DA:82,1
DA:86,1
DA:87,1
DA:88,1
DA:89,1
DA:90,1
DA:91,1
DA:92,1
DA:93,1
LF:60
LH:60
end_of_record
SF:src/data/models/woocommerce/__init__.py
end_of_record
SF:src/data/models/woocommerce/category.py
DA:1,1
DA:3,1
DA:4,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:13,1
DA:14,1
LF:11
LH:11
end_of_record
SF:src/data/models/woocommerce/product.py
DA:1,1
DA:2,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
LF:8
LH:8
end_of_record
SF:src/data/woocommerce_category_datasource.py
DA:1,1
DA:3,1
DA:6,1
DA:7,1
DA:8,0
DA:10,1
DA:11,0
DA:12,0
DA:13,0
DA:14,0
DA:15,0
DA:16,0
DA:17,0
DA:18,0
DA:19,0
DA:20,0
DA:21,0
DA:23,1
DA:24,0
DA:25,0
DA:26,0
DA:27,0
DA:29,1
DA:30,0
DA:31,0
DA:32,0
DA:33,0
DA:35,1
DA:39,0
DA:40,0
DA:42,1
DA:43,0
LF:32
LH:9
FN:7,8,WoocommerceCategoryDatasource.__init__
FNDA:0,WoocommerceCategoryDatasource.__init__
FN:10,21,WoocommerceCategoryDatasource.get_categories
FNDA:0,WoocommerceCategoryDatasource.get_categories
FN:23,27,WoocommerceCategoryDatasource.create
FNDA:0,WoocommerceCategoryDatasource.create
FN:29,33,WoocommerceCategoryDatasource.update
FNDA:0,WoocommerceCategoryDatasource.update
FN:35,40,WoocommerceCategoryDatasource.delete
FNDA:0,WoocommerceCategoryDatasource.delete
FN:42,51,WoocommerceCategoryDatasource._map_response_category
FNDA:0,WoocommerceCategoryDatasource._map_response_category
FNF:6
FNH:0
end_of_record
SF:src/data/woocommerce_category_repository_impl.py
DA:1,1
DA:2,1
DA:3,1
DA:4,1
DA:6,1
DA:7,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:15,1
DA:21,1
DA:25,0
DA:26,0
DA:28,1
DA:34,0
DA:35,0
DA:36,0
DA:38,0
DA:40,0
DA:41,0
DA:42,0
DA:43,0
DA:45,0
DA:46,0
DA:47,0
DA:48,0
DA:50,0
DA:51,0
DA:54,0
DA:55,0
DA:56,0
DA:57,0
DA:58,0
DA:59,0
DA:61,1
DA:65,0
DA:66,0
DA:67,0
DA:68,0
DA:70,1
DA:75,0
DA:76,0
DA:77,0
DA:78,0
DA:80,0
DA:82,1
DA:86,0
DA:87,0
DA:88,0
DA:89,0
DA:91,1
DA:95,0
DA:97,1
DA:102,0
DA:103,0
DA:104,0
DA:105,0
DA:106,0
DA:107,0
DA:108,0
DA:109,0
DA:111,1
DA:115,0
DA:116,0
DA:117,0
DA:118,0
DA:120,1
DA:126,0
DA:131,0
DA:136,0
DA:137,0
DA:139,1
DA:151,0
DA:152,0
DA:153,0
DA:154,0
DA:155,0
DA:162,0
DA:163,0
DA:164,0
LF:81
LH:21
FN:21,26,WoocommerceCategoryRepositoryImpl.__init__
FNDA:0,WoocommerceCategoryRepositoryImpl.__init__
FN:28,59,WoocommerceCategoryRepositoryImpl.upsert
FNDA:0,WoocommerceCategoryRepositoryImpl.upsert
FN:61,68,WoocommerceCategoryRepositoryImpl._build_used_slugs_by_parent
FNDA:0,WoocommerceCategoryRepositoryImpl._build_used_slugs_by_parent
FN:70,80,WoocommerceCategoryRepositoryImpl._flatten_categories
FNDA:0,WoocommerceCategoryRepositoryImpl._flatten_categories
FN:75,78,WoocommerceCategoryRepositoryImpl._flatten_categories.recurse
FNDA:0,WoocommerceCategoryRepositoryImpl._flatten_categories.recurse
FN:82,89,WoocommerceCategoryRepositoryImpl._get_english_label
FNDA:0,WoocommerceCategoryRepositoryImpl._get_english_label
FN:91,95,WoocommerceCategoryRepositoryImpl._generate_slug
FNDA:0,WoocommerceCategoryRepositoryImpl._generate_slug
FN:97,109,WoocommerceCategoryRepositoryImpl._generate_unique_slug
FNDA:0,WoocommerceCategoryRepositoryImpl._generate_unique_slug
FN:111,118,WoocommerceCategoryRepositoryImpl._get_parent_id
FNDA:0,WoocommerceCategoryRepositoryImpl._get_parent_id
FN:120,137,WoocommerceCategoryRepositoryImpl._update_category
FNDA:0,WoocommerceCategoryRepositoryImpl._update_category
FN:139,164,WoocommerceCategoryRepositoryImpl._create_category
FNDA:0,WoocommerceCategoryRepositoryImpl._create_category
FNF:11
FNH:0
end_of_record
SF:src/data/woocommerce_product_datasource.py
DA:1,1
DA:3,1
DA:6,1
DA:7,1
DA:10,1
DA:11,1
DA:12,0
DA:14,1
DA:15,0
DA:16,0
DA:18,0
DA:19,0
DA:20,0
DA:21,0
DA:23,1
DA:24,0
DA:25,0
DA:26,0
DA:27,0
DA:29,1
DA:30,0
DA:31,0
DA:32,0
DA:33,0
DA:35,1
DA:36,0
DA:37,0
DA:39,1
DA:40,0
LF:29
LH:11
FN:11,12,WoocommerceProductDatasource.__init__
FNDA:0,WoocommerceProductDatasource.__init__
FN:14,21,WoocommerceProductDatasource.get_product_by_sku
FNDA:0,WoocommerceProductDatasource.get_product_by_sku
FN:23,27,WoocommerceProductDatasource.create
FNDA:0,WoocommerceProductDatasource.create
FN:29,33,WoocommerceProductDatasource.update
FNDA:0,WoocommerceProductDatasource.update
FN:35,37,WoocommerceProductDatasource.delete
FNDA:0,WoocommerceProductDatasource.delete
FN:39,46,WoocommerceProductDatasource._map_response_product
FNDA:0,WoocommerceProductDatasource._map_response_product
FNF:6
FNH:0
end_of_record
SF:src/data/woocommerce_product_repository_impl.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:12,1
DA:13,1
DA:19,1
DA:20,1
DA:21,1
DA:23,1
DA:29,1
DA:30,0
DA:32,0
DA:33,0
DA:34,1
DA:35,1
DA:36,0
DA:38,1
DA:39,1
DA:40,1
DA:41,0
DA:43,1
DA:44,0
DA:45,0
DA:47,1
DA:48,1
DA:49,1
DA:50,0
DA:53,1
DA:65,1
DA:66,0
DA:67,0
DA:68,0
DA:69,0
DA:70,0
LF:39
LH:26
FN:13,21,WoocommerceProductRepositoryImpl.__init__
FNDA:1,WoocommerceProductRepositoryImpl.__init__
FN:23,36,WoocommerceProductRepositoryImpl.upsert
FNDA:1,WoocommerceProductRepositoryImpl.upsert
FN:38,41,WoocommerceProductRepositoryImpl.create
FNDA:1,WoocommerceProductRepositoryImpl.create
FN:43,45,WoocommerceProductRepositoryImpl.update
FNDA:0,WoocommerceProductRepositoryImpl.update
FN:47,63,WoocommerceProductRepositoryImpl._build_payload
FNDA:1,WoocommerceProductRepositoryImpl._build_payload
FN:65,70,WoocommerceProductRepositoryImpl._get_description
FNDA:0,WoocommerceProductRepositoryImpl._get_description
FNF:6
FNH:4
end_of_record
SF:src/domain/__init__.py
end_of_record
SF:src/domain/models/__init__.py
end_of_record
SF:src/domain/models/asset.py
DA:1,1
DA:2,1
DA:3,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:10,1
DA:13,1
DA:14,1
DA:17,1
DA:18,1
DA:21,1
DA:22,1
DA:23,1
DA:26,1
DA:27,1
DA:30,1
DA:31,1
DA:32,1
DA:33,1
DA:34,1
DA:35,1
DA:36,1
DA:37,1
DA:38,1
DA:39,1
DA:40,1
DA:41,1
DA:44,1
DA:45,1
DA:46,1
DA:47,1
DA:49,1
DA:50,1
DA:51,0
DA:54,1
DA:55,1
DA:56,1
DA:57,1
DA:59,1
DA:60,0
DA:66,1
DA:67,1
DA:68,1
DA:69,1
DA:70,1
DA:71,1
DA:72,1
DA:73,1
DA:74,1
DA:76,1
DA:77,1
DA:82,1
DA:83,1
DA:85,1
DA:86,1
LF:57
LH:55
FN:50,51,TargetGroup.is_active
FNDA:0,TargetGroup.is_active
FN:59,63,Segmentation.get_target_group_by_name
FNDA:0,Segmentation.get_target_group_by_name
FN:76,80,AssetDetails.get_translation
FNDA:1,AssetDetails.get_translation
FN:82,83,AssetDetails.get_first_file_for_char
FNDA:1,AssetDetails.get_first_file_for_char
FN:85,93,AssetDetails.get_file_for_char_and_language
FNDA:1,AssetDetails.get_file_for_char_and_language
FNF:5
FNH:3
end_of_record
SF:src/domain/models/directory_file.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:8,1
DA:9,1
DA:11,1
DA:12,1
DA:13,1
LF:9
LH:9
end_of_record
SF:src/domain/models/document.py
DA:1,1
DA:2,1
DA:4,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:13,1
DA:18,1
DA:19,1
DA:20,1
DA:21,1
DA:22,1
DA:23,1
DA:24,1
DA:25,1
DA:26,1
DA:27,1
DA:29,1
DA:30,1
DA:31,1
LF:21
LH:21
end_of_record
SF:src/domain/models/document_kind.py
DA:1,1
DA:4,1
DA:9,1
DA:10,1
LF:4
LH:4
end_of_record
SF:src/domain/models/product.py
DA:1,1
DA:2,1
DA:3,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:10,1
DA:13,1
DA:14,1
DA:17,1
DA:18,1
DA:21,1
DA:22,1
DA:25,1
DA:26,1
DA:27,1
DA:30,1
DA:31,1
DA:32,1
DA:35,1
DA:36,1
DA:37,1
DA:40,1
DA:41,1
DA:42,1
DA:45,1
DA:46,1
DA:49,1
DA:50,1
DA:51,1
DA:54,1
DA:55,1
DA:56,1
DA:57,1
DA:58,1
DA:59,1
DA:60,1
DA:63,1
DA:64,1
DA:67,1
DA:68,1
DA:69,1
DA:71,1
DA:72,1
DA:75,1
DA:76,1
DA:79,1
DA:80,1
DA:82,1
DA:83,1
DA:86,1
DA:89,1
DA:91,1
DA:92,0
DA:95,1
DA:96,1
DA:99,1
DA:100,1
DA:103,1
DA:104,1
DA:105,1
DA:107,1
DA:108,0
DA:113,1
DA:114,1
DA:115,1
DA:117,1
DA:118,1
DA:120,1
DA:121,1
DA:122,1
DA:123,0
DA:126,1
DA:127,1
DA:128,1
DA:129,1
DA:130,1
DA:132,1
DA:133,1
DA:136,1
DA:137,1
DA:138,1
DA:139,1
DA:142,1
DA:143,1
DA:144,1
DA:145,1
DA:146,1
DA:149,1
DA:150,1
DA:151,1
DA:154,1
DA:155,1
DA:156,1
DA:159,1
DA:160,1
DA:161,1
DA:162,1
DA:163,1
DA:165,1
DA:166,0
DA:171,1
DA:172,1
DA:173,1
DA:174,1
DA:175,1
DA:176,1
DA:177,1
DA:180,1
DA:181,1
DA:184,1
DA:187,1
DA:188,1
DA:189,1
DA:190,1
DA:191,1
DA:192,1
DA:193,1
DA:194,1
LF:120
LH:116
FN:71,72,ProductText.get_translation
FNDA:1,ProductText.get_translation
FN:82,83,ProductName.get_translation
FNDA:1,ProductName.get_translation
FN:91,92,Translations.get_translation
FNDA:0,Translations.get_translation
FN:107,110,AsssetSubType.get_translation
FNDA:0,AsssetSubType.get_translation
FN:117,118,ProductAsset.__hash__
FNDA:1,ProductAsset.__hash__
FN:120,123,ProductAsset.__eq__
FNDA:1,ProductAsset.__eq__
FN:132,133,ProductAssets.get_asset_id_for_role
FNDA:1,ProductAssets.get_asset_id_for_role
FN:165,168,AttributeValue.get_translation
FNDA:0,AttributeValue.get_translation
FNF:8
FNH:5
end_of_record
SF:src/domain/models/proposition.py
DA:1,1
DA:2,1
DA:4,1
DA:5,1
DA:6,1
DA:8,1
DA:11,1
DA:12,1
DA:16,1
DA:17,1
DA:18,1
DA:19,1
DA:20,1
DA:21,1
DA:22,1
DA:25,1
DA:26,1
DA:27,1
DA:31,1
DA:32,1
DA:33,1
DA:34,1
DA:35,1
DA:36,1
DA:37,1
DA:39,1
DA:43,1
DA:44,1
DA:45,1
LF:29
LH:29
end_of_record
SF:src/domain/models/storage/__init__.py
end_of_record
SF:src/domain/models/storage/result.py
DA:2,0
DA:4,0
DA:5,0
DA:6,0
DA:7,0
DA:8,0
DA:9,0
DA:10,0
DA:11,0
DA:12,0
DA:13,0
DA:14,0
DA:15,0
DA:17,0
DA:18,0
DA:20,0
DA:23,0
DA:24,0
DA:25,0
DA:26,0
DA:27,0
DA:28,0
DA:29,0
DA:32,0
DA:37,0
DA:38,0
DA:39,0
DA:47,0
DA:48,0
DA:50,0
DA:51,0
DA:53,0
DA:54,0
DA:56,0
DA:57,0
DA:59,0
DA:60,0
DA:62,0
DA:63,0
DA:65,0
DA:66,0
DA:68,0
DA:72,0
DA:74,0
DA:78,0
DA:80,0
DA:81,0
DA:85,0
DA:87,0
DA:91,0
DA:93,0
DA:97,0
DA:99,0
DA:103,0
DA:105,0
DA:109,0
DA:111,0
DA:115,0
DA:117,0
DA:121,0
DA:123,0
DA:127,0
DA:129,0
DA:134,0
DA:136,0
DA:141,0
DA:143,0
DA:148,0
DA:150,0
DA:154,0
DA:156,0
DA:161,0
DA:163,0
DA:167,0
DA:170,0
DA:175,0
DA:176,0
DA:178,0
DA:179,0
DA:181,0
DA:182,0
DA:184,0
DA:185,0
DA:187,0
DA:188,0
DA:190,0
DA:191,0
DA:193,0
DA:194,0
DA:196,0
DA:197,0
DA:199,0
DA:203,0
DA:205,0
DA:209,0
DA:211,0
DA:212,0
DA:216,0
DA:218,0
DA:222,0
DA:224,0
DA:228,0
DA:230,0
DA:234,0
DA:236,0
DA:240,0
DA:242,0
DA:246,0
DA:248,0
DA:253,0
DA:255,0
DA:259,0
DA:261,0
DA:265,0
DA:267,0
DA:271,0
DA:273,0
DA:277,0
DA:279,0
DA:284,0
DA:286,0
DA:290,0
DA:292,0
DA:297,0
DA:302,0
DA:307,0
DA:309,0
DA:313,0
DA:316,0
DA:326,0
DA:328,0
DA:329,0
DA:330,0
DA:332,0
DA:333,0
DA:337,0
DA:340,0
DA:348,0
DA:351,0
DA:353,0
DA:358,0
DA:359,0
DA:360,0
DA:361,0
DA:362,0
DA:363,0
DA:365,0
DA:367,0
LF:148
LH:0
FN:42,42,Ok.__init__
FNDA:0,Ok.__init__
FN:45,45,Ok.__init__
FNDA:0,Ok.__init__
FN:47,48,Ok.__init__
FNDA:0,Ok.__init__
FN:50,51,Ok.__repr__
FNDA:0,Ok.__repr__
FN:53,54,Ok.__eq__
FNDA:0,Ok.__eq__
FN:56,57,Ok.__ne__
FNDA:0,Ok.__ne__
FN:59,60,Ok.__hash__
FNDA:0,Ok.__hash__
FN:62,63,Ok.is_ok
FNDA:0,Ok.is_ok
FN:65,66,Ok.is_err
FNDA:0,Ok.is_err
FN:68,72,Ok.ok
FNDA:0,Ok.ok
FN:74,78,Ok.err
FNDA:0,Ok.err
FN:81,85,Ok.value
FNDA:0,Ok.value
FN:87,91,Ok.expect
FNDA:0,Ok.expect
FN:93,97,Ok.expect_err
FNDA:0,Ok.expect_err
FN:99,103,Ok.unwrap
FNDA:0,Ok.unwrap
FN:105,109,Ok.unwrap_err
FNDA:0,Ok.unwrap_err
FN:111,115,Ok.unwrap_or
FNDA:0,Ok.unwrap_or
FN:117,121,Ok.unwrap_or_else
FNDA:0,Ok.unwrap_or_else
FN:123,127,Ok.unwrap_or_raise
FNDA:0,Ok.unwrap_or_raise
FN:129,134,Ok.map
FNDA:0,Ok.map
FN:136,141,Ok.map_or
FNDA:0,Ok.map_or
FN:143,148,Ok.map_or_else
FNDA:0,Ok.map_or_else
FN:150,154,Ok.map_err
FNDA:0,Ok.map_err
FN:156,161,Ok.and_then
FNDA:0,Ok.and_then
FN:163,167,Ok.or_else
FNDA:0,Ok.or_else
FN:178,179,Err.__init__
FNDA:0,Err.__init__
FN:181,182,Err.__repr__
FNDA:0,Err.__repr__
FN:184,185,Err.__eq__
FNDA:0,Err.__eq__
FN:187,188,Err.__ne__
FNDA:0,Err.__ne__
FN:190,191,Err.__hash__
FNDA:0,Err.__hash__
FN:193,194,Err.is_ok
FNDA:0,Err.is_ok
FN:196,197,Err.is_err
FNDA:0,Err.is_err
FN:199,203,Err.ok
FNDA:0,Err.ok
FN:205,209,Err.err
FNDA:0,Err.err
FN:212,216,Err.value
FNDA:0,Err.value
FN:218,222,Err.expect
FNDA:0,Err.expect
FN:224,228,Err.expect_err
FNDA:0,Err.expect_err
FN:230,234,Err.unwrap
FNDA:0,Err.unwrap
FN:236,240,Err.unwrap_err
FNDA:0,Err.unwrap_err
FN:242,246,Err.unwrap_or
FNDA:0,Err.unwrap_or
FN:248,253,Err.unwrap_or_else
FNDA:0,Err.unwrap_or_else
FN:255,259,Err.unwrap_or_raise
FNDA:0,Err.unwrap_or_raise
FN:261,265,Err.map
FNDA:0,Err.map
FN:267,271,Err.map_or
FNDA:0,Err.map_or
FN:273,277,Err.map_or_else
FNDA:0,Err.map_or_else
FN:279,284,Err.map_err
FNDA:0,Err.map_err
FN:286,290,Err.and_then
FNDA:0,Err.and_then
FN:292,297,Err.or_else
FNDA:0,Err.or_else
FN:328,330,UnwrapError.__init__
FNDA:0,UnwrapError.__init__
FN:333,337,UnwrapError.result
FNDA:0,UnwrapError.result
FN:340,367,as_result
FNDA:0,as_result
FN:353,365,as_result.decorator
FNDA:0,as_result.decorator
FN:359,363,as_result.decorator.wrapper
FNDA:0,as_result.decorator.wrapper
FNF:53
FNH:0
end_of_record
SF:src/domain/models/sync_config.py
DA:1,1
DA:2,1
DA:4,1
DA:7,1
DA:8,1
DA:9,1
DA:12,1
DA:13,1
DA:14,1
LF:9
LH:9
end_of_record
SF:src/domain/models/sync_metadata/__init__.py
end_of_record
SF:src/domain/models/sync_metadata/sync_metadata_directory_file.py
DA:1,1
DA:3,1
DA:6,1
DA:7,1
DA:8,1
LF:5
LH:5
end_of_record
SF:src/domain/repositories/__init__.py
end_of_record
SF:src/domain/repositories/datahub_product_repository.py
DA:1,1
DA:2,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:14,1
DA:16,1
DA:17,1
DA:20,1
DA:21,1
DA:23,0
DA:25,1
DA:26,1
DA:28,0
DA:30,1
DA:31,1
DA:33,0
DA:35,1
DA:36,1
DA:38,0
DA:40,1
DA:41,1
DA:43,0
LF:28
LH:23
FN:17,18,SourceProductRepository.get_file_contents
FNDA:0,SourceProductRepository.get_file_contents
FN:21,23,SourceProductRepository.get_directory_files
FNDA:0,SourceProductRepository.get_directory_files
FN:26,28,SourceProductRepository.get_last_modified_header
FNDA:0,SourceProductRepository.get_last_modified_header
FN:31,33,SourceProductRepository.get_product_by_id
FNDA:0,SourceProductRepository.get_product_by_id
FN:36,38,SourceProductRepository.get_proposition_by_id
FNDA:0,SourceProductRepository.get_proposition_by_id
FN:41,43,SourceProductRepository.get_asset_by_id
FNDA:0,SourceProductRepository.get_asset_by_id
FNF:6
FNH:0
end_of_record
SF:src/domain/repositories/local_storage_repository.py
DA:1,1
DA:2,1
DA:4,1
DA:5,1
DA:8,1
DA:13,1
DA:14,1
DA:18,0
DA:20,1
DA:21,1
DA:25,0
LF:11
LH:9
FN:14,18,AbstractFileStorageRepository.write_sync_metadata
FNDA:0,AbstractFileStorageRepository.write_sync_metadata
FN:21,25,AbstractFileStorageRepository.read_sync_metadata
FNDA:0,AbstractFileStorageRepository.read_sync_metadata
FNF:2
FNH:0
end_of_record
SF:src/domain/repositories/target_category_repository.py
DA:1,1
DA:2,1
DA:4,1
DA:7,1
DA:8,1
DA:9,1
DA:13,0
LF:7
LH:6
FN:9,13,TargetCategoryRepository.upsert
FNDA:0,TargetCategoryRepository.upsert
FNF:1
FNH:0
end_of_record
SF:src/domain/repositories/target_product_repository.py
DA:1,1
DA:2,1
DA:4,1
DA:5,1
DA:8,1
DA:9,1
DA:10,1
DA:16,0
LF:8
LH:7
FN:10,16,TargetProductRepository.upsert
FNDA:0,TargetProductRepository.upsert
FNF:1
FNH:0
end_of_record
SF:src/exceptions.py
DA:1,1
DA:2,1
DA:3,1
DA:6,1
DA:12,1
DA:13,0
DA:14,0
DA:15,0
DA:17,1
DA:18,0
DA:19,0
DA:20,0
DA:23,1
DA:27,1
LF:14
LH:8
FN:12,15,CentixLoadException.__init__
FNDA:0,CentixLoadException.__init__
FN:17,20,CentixLoadException.__str__
FNDA:0,CentixLoadException.__str__
FNF:2
FNH:0
end_of_record
SF:src/interactors/__init__.py
end_of_record
SF:src/interactors/sync_interactor.py
DA:1,1
DA:2,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:11,1
DA:13,1
DA:14,1
DA:15,1
DA:16,1
DA:17,1
DA:18,1
DA:19,1
DA:20,1
DA:21,1
DA:22,1
DA:23,1
DA:24,1
DA:26,1
DA:29,1
DA:30,1
DA:38,1
DA:39,1
DA:40,1
DA:41,1
DA:42,1
DA:44,1
DA:45,1
DA:46,1
DA:47,1
DA:48,1
DA:49,1
DA:50,1
DA:51,1
DA:52,1
DA:55,1
DA:60,1
DA:65,1
DA:70,1
DA:71,1
DA:74,1
DA:75,0
DA:78,1
DA:81,1
DA:82,1
DA:83,1
DA:84,1
DA:85,1
DA:87,1
DA:90,1
DA:92,1
DA:93,1
DA:94,1
DA:96,1
DA:97,1
DA:98,1
DA:100,1
DA:101,1
DA:102,1
DA:106,1
DA:111,1
DA:112,1
DA:114,1
DA:115,0
DA:118,1
DA:119,1
DA:120,1
DA:121,1
DA:122,1
DA:124,0
DA:127,1
DA:128,1
DA:129,1
DA:130,1
DA:131,1
DA:132,1
DA:133,0
DA:134,0
DA:135,0
DA:138,1
DA:145,1
DA:146,1
DA:149,1
DA:150,1
DA:153,0
DA:154,0
DA:156,1
DA:158,1
DA:160,1
DA:162,1
DA:163,1
DA:164,1
DA:165,1
DA:166,0
DA:168,1
DA:170,1
DA:171,1
DA:173,1
DA:174,1
DA:176,1
DA:178,1
DA:182,1
DA:183,0
DA:189,0
DA:191,1
DA:193,1
DA:197,1
DA:198,1
DA:199,1
DA:200,1
DA:201,1
DA:202,1
DA:203,1
DA:205,1
DA:206,1
DA:210,1
DA:212,1
DA:213,1
DA:214,1
DA:216,1
DA:223,1
DA:224,1
DA:225,1
DA:226,1
DA:227,1
DA:228,1
DA:229,1
DA:230,1
DA:231,1
DA:232,1
LF:134
LH:123
FN:30,42,SyncInteractor.__init__
FNDA:1,SyncInteractor.__init__
FN:44,156,SyncInteractor.execute
FNDA:1,SyncInteractor.execute
FN:158,166,SyncInteractor._extract_uuid
FNDA:1,SyncInteractor._extract_uuid
FN:168,176,SyncInteractor._download_asset
FNDA:1,SyncInteractor._download_asset
FN:178,191,SyncInteractor._filter_synced_products
FNDA:1,SyncInteractor._filter_synced_products
FN:193,214,SyncInteractor._collect_category_paths_for_product
FNDA:1,SyncInteractor._collect_category_paths_for_product
FN:216,232,SyncInteractor._collect_all_product_umids
FNDA:1,SyncInteractor._collect_all_product_umids
FNF:7
FNH:7
end_of_record
SF:src/main.py
DA:1,0
DA:2,0
DA:3,0
DA:5,0
DA:6,0
DA:8,0
DA:9,0
DA:10,0
DA:11,0
DA:13,0
DA:15,0
DA:16,0
DA:17,0
DA:19,0
DA:24,0
DA:31,0
DA:34,0
DA:35,0
DA:36,0
DA:37,0
DA:38,0
DA:39,0
DA:40,0
DA:41,0
DA:42,0
DA:43,0
DA:44,0
DA:45,0
DA:48,0
DA:49,0
DA:51,0
DA:56,0
DA:57,0
DA:58,0
DA:61,0
DA:62,0
LF:36
LH:0
FN:34,45,configure_logging
FNDA:0,configure_logging
FN:48,58,main
FNDA:0,main
FNF:2
FNH:0
end_of_record
SF:src/repository_factories/__init__.py
end_of_record
SF:src/repository_factories/datahub_to_centix_repository_factory.py
DA:1,1
DA:2,1
DA:3,1
DA:4,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:13,1
DA:15,1
DA:18,1
DA:21,1
DA:22,1
DA:23,1
DA:24,1
DA:26,1
DA:27,1
DA:29,1
DA:30,1
DA:32,1
DA:33,1
DA:34,1
DA:35,1
DA:37,1
DA:41,1
DA:42,1
DA:44,1
DA:45,1
LF:31
LH:31
FN:21,39,DataHubToCentixRepositoryFactory.create_target_product_repository
FNDA:1,DataHubToCentixRepositoryFactory.create_target_product_repository
FN:41,42,DataHubToCentixRepositoryFactory.create_target_category_repository
FNDA:1,DataHubToCentixRepositoryFactory.create_target_category_repository
FN:44,48,DataHubToCentixRepositoryFactory.create_source_repository
FNDA:1,DataHubToCentixRepositoryFactory.create_source_repository
FNF:3
FNH:3
end_of_record
SF:src/repository_factories/datahub_to_woocommerce_repository_factory.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:11,1
DA:14,1
DA:15,1
DA:22,1
DA:23,1
DA:24,1
DA:25,0
DA:26,0
DA:28,1
DA:29,0
DA:30,0
DA:31,0
DA:33,1
DA:34,0
LF:21
LH:15
FN:14,20,DataHubToWoocommerceRepositoryFactory._create_wc_api
FNDA:1,DataHubToWoocommerceRepositoryFactory._create_wc_api
FN:22,26,DataHubToWoocommerceRepositoryFactory.create_target_product_repository
FNDA:1,DataHubToWoocommerceRepositoryFactory.create_target_product_repository
FN:28,31,DataHubToWoocommerceRepositoryFactory.create_target_category_repository
FNDA:0,DataHubToWoocommerceRepositoryFactory.create_target_category_repository
FN:33,37,DataHubToWoocommerceRepositoryFactory.create_source_repository
FNDA:0,DataHubToWoocommerceRepositoryFactory.create_source_repository
FNF:4
FNH:2
end_of_record
SF:src/repository_factories/repository_factory.py
DA:1,1
DA:2,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:10,1
DA:13,1
DA:14,1
DA:15,1
DA:17,1
DA:18,1
DA:20,0
DA:22,1
DA:23,1
DA:25,0
DA:27,1
DA:28,1
DA:30,0
LF:19
LH:16
FN:13,15,RepositoryFactory.__init__
FNDA:1,RepositoryFactory.__init__
FN:18,20,RepositoryFactory.create_target_product_repository
FNDA:0,RepositoryFactory.create_target_product_repository
FN:23,25,RepositoryFactory.create_target_category_repository
FNDA:0,RepositoryFactory.create_target_category_repository
FN:28,30,RepositoryFactory.create_source_repository
FNDA:0,RepositoryFactory.create_source_repository
FNF:4
FNH:1
end_of_record
SF:src/shared/__init__.py
end_of_record
SF:src/shared/enum.py
DA:1,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:13,1
DA:16,1
DA:17,1
DA:18,1
DA:19,1
DA:20,1
DA:21,1
DA:22,1
DA:25,1
DA:26,1
DA:29,1
DA:32,1
DA:33,1
DA:34,1
DA:35,1
DA:38,1
DA:39,1
DA:40,1
DA:41,1
DA:42,1
DA:43,1
DA:44,1
DA:45,1
DA:48,1
DA:49,1
DA:50,1
DA:51,1
DA:52,1
DA:53,1
DA:54,1
DA:55,1
DA:56,1
DA:57,1
DA:58,1
DA:59,1
DA:60,1
DA:61,1
DA:62,1
DA:63,1
DA:64,1
DA:65,1
DA:66,1
DA:67,1
DA:68,1
DA:69,1
DA:72,1
DA:73,1
DA:74,1
DA:77,1
DA:78,1
DA:79,1
DA:80,1
DA:81,1
LF:63
LH:63
end_of_record
SF:src/shared/env.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:9,1
DA:10,1
DA:12,1
DA:13,1
DA:14,1
DA:16,1
DA:17,1
DA:19,1
DA:20,1
DA:21,1
DA:22,1
DA:24,1
DA:25,1
DA:28,1
DA:29,1
DA:30,1
DA:31,1
DA:33,1
DA:35,1
DA:36,1
DA:37,1
DA:38,1
LF:27
LH:27
end_of_record
SF:src/shared/field_sanitizer.py
DA:1,1
DA:4,1
DA:5,1
DA:6,0
DA:8,1
DA:9,0
DA:10,0
DA:11,1
LF:8
LH:5
FN:4,11,sanitize_length
FNDA:1,sanitize_length
FNF:1
FNH:1
end_of_record
SF:src/shared/utils/__init__.py
end_of_record
SF:src/shared/utils/api_client_wrapper.py
DA:1,1
DA:2,1
DA:3,1
DA:4,1
DA:6,1
DA:7,1
DA:8,1
DA:10,1
DA:11,1
DA:12,1
DA:14,1
DA:15,1
DA:16,1
DA:18,1
DA:19,1
LF:15
LH:15
FN:2,4,ApiClientWrapper.__init__
FNDA:1,ApiClientWrapper.__init__
FN:6,8,ApiClientWrapper._refresh_token
FNDA:1,ApiClientWrapper._refresh_token
FN:10,19,ApiClientWrapper.__getattr__
FNDA:1,ApiClientWrapper.__getattr__
FN:14,16,ApiClientWrapper.__getattr__.wrapped
FNDA:1,ApiClientWrapper.__getattr__.wrapped
FNF:4
FNH:4
end_of_record
SF:src/shared/utils/auth.py
DA:1,1
DA:2,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:9,1
DA:11,1
DA:12,1
DA:15,1
DA:16,1
DA:17,1
DA:18,1
DA:19,1
DA:20,1
DA:22,1
DA:23,1
DA:24,1
DA:25,1
DA:26,1
DA:27,1
DA:37,1
DA:38,1
DA:40,1
DA:41,0
DA:42,0
DA:44,1
DA:45,1
DA:47,1
DA:48,1
DA:49,1
DA:50,1
DA:51,1
DA:53,0
DA:54,0
DA:55,1
DA:56,1
DA:57,1
DA:58,0
LF:39
LH:34
FN:16,20,AuthorizationToken.__init__
FNDA:1,AuthorizationToken.__init__
FN:22,58,AuthorizationToken.refresh_token
FNDA:1,AuthorizationToken.refresh_token
FNF:2
FNH:2
end_of_record
SF:src/sync_interactor_factory.py
DA:1,1
DA:2,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:10,1
DA:11,1
DA:12,1
DA:13,1
DA:18,1
DA:19,1
DA:20,0
DA:22,1
DA:24,1
LF:16
LH:15
FN:12,30,SyncInteractorFactory.build_interactor
FNDA:1,SyncInteractorFactory.build_interactor
FNF:1
FNH:1
end_of_record
