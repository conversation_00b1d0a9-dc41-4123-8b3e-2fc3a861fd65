.PHONY: run fix qa test-unit test-integration dev security sleep

run:
	@echo "Running the application..."
	@poetry run python -m src.main

fix:
	@echo "Cleaning up code..."
	@poetry run autoflake .
	@poetry run black .
	@poetry run isort .
	@poetry run mypy --explicit-package-bases . --exclude http_api_clients/
	@echo "Code cleanup completed successfully"

qa:
	@echo "Running quality checks on code..."
	@poetry run flake8
	@echo "Quality checks completed successfully"

test:
	@$(MAKE) test-unit
	@$(MAKE) test-integration

test-unit:
	@echo "Running unit tests (parallel, random order)..."
	@poetry run pytest \
		-m "not sequential" \
		-n auto \
		--dist loadfile \
		--random-order \
		--log-level=INFO

test-integration:
	@echo "Running sequential integration tests..."
	@poetry run pytest \
		-m "sequential" \
		--cov=./src \
		--cov-append \
		--cov-report=term-missing:skip-covered \
		--cov-report=lcov:coverage.info \
		--junitxml=test-results.xml \
		--log-level=INFO

dev:
	@echo "Starting the dev env of Centix Load.."
	@poetry run python -m script.update

security:
	@echo "Running security checks..."
	@poetry run bandit -c pyproject.toml -r . --exclude http_api_clients/
	@echo "Security checks completed successfully"

sleep:
	@sleep 3600
